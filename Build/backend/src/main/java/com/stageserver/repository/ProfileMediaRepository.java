package com.stageserver.repository;

import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.profile.RiderDetails;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProfileMediaRepository extends Neo4jRepository<ProfileMedia, String> {
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_MEDIA]-(m:ProfileMedia) RETURN m")
    Optional<ProfileMedia> findByProfileId(String profileId);

    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_MEDIA]-(m:ProfileMedia)-[r1:HAS_RIDER_DETAILS]-(riderDetails) RETURN riderDetails")
    Optional<List<RiderDetails>> findByRideDetailsForAct(String profileId);
}
