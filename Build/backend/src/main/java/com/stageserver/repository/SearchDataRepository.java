package com.stageserver.repository;

import com.stageserver.model.search.SearchData;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SearchDataRepository extends Neo4jRepository<SearchData, String> {
    @Query("MATCH (u:User {email: $email})-[:HAS_SEARCH_DATA]->(s:SearchData) RETURN s")
    List<SearchData> findForUser(String email);

    @Query("MATCH (u:User {email: $email})-[:HAS_SEARCH_DATA]->(s:SearchData {searchName: $searchName}) RETURN s")
    Optional<SearchData> findByNameForUser(String email, String searchName);
}
