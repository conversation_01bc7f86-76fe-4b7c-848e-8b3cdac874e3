package com.stageserver.repository;

import com.stageserver.model.profile.EntertainmentType;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EntertainmentTypeRepository extends Neo4jRepository<EntertainmentType, String> {
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_SKILLS]-(l:ActSkills)-[r2:HAS_ENTERTAINMENT_TYPE]-(l2:EntertainmentType) RETURN l2")
    Optional<EntertainmentType> findByProfileId(String profileId);

}
