package com.stageserver.repository.contract;

import com.stageserver.model.contract.ActRiderChanges;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ActRiderChangesRepository extends Neo4jRepository<ActRiderChanges, String> {

    @Query("MATCH (c:Contract {contractId: $contractId})-[:ACT_RIDER_CHANGES]->(s:ActRiderChanges) RETURN s")
    Optional<ActRiderChanges> findByContractId(String contractId);
}
