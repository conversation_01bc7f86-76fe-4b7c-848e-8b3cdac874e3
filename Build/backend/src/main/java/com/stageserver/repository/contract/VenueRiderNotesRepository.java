package com.stageserver.repository.contract;

import com.stageserver.model.contract.VenueRiderNotes;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VenueRiderNotesRepository extends Neo4jRepository<VenueRiderNotes, String> {

    @Query("MATCH (c:Contract {contractId: $contractId})-[:VENUE_RIDER_NOTES]->(s:VenueRiderNotes) RETURN s")
    Optional<VenueRiderNotes> findByContractId(String contractId);
}
