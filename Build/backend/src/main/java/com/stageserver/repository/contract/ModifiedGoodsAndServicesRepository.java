package com.stageserver.repository.contract;

import com.stageserver.model.contract.ModifiedGoodsAndServices;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface ModifiedGoodsAndServicesRepository extends Neo4jRepository<ModifiedGoodsAndServices, String> {

    @Query("MATCH(m:ModifiedGoodsAndServices {contractId: $contractId}) RETURN m")
    Optional<ModifiedGoodsAndServices> findByContractId(String contractId);
}
