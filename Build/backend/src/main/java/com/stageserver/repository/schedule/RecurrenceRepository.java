package com.stageserver.repository.schedule;

import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RecurrenceRepository extends Neo4jRepository<Recurrence, String> {


   @Query("MATCH (p:Profile {profileId: $profileId})-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE elementId(s)=$elementId MATCH (s)-[:RECURRENCE]->(r:Recurrence) RETURN r")
    Recurrence getRecurrenceByProfileId(String profileId, String elementId);

    @Query("MATCH (p:Event {eventId: $eventId})-[:HAS_SCHEDULE]->(s:ScheduleTime) WHERE elementId(s)=$elementId MATCH (s)-[:RECURRENCE]->(r:Recurrence) RETURN r")
   Recurrence getRecurrenceByEventId(String eventId, String elementId);

    @Query("MATCH (s:ScheduleTime{scheduleId:$scheduleId})-[:RECURRENCE]->(r:Recurrence) RETURN r")
    Optional<Recurrence> getRecurrenceByScheduleId(String scheduleId);
}
