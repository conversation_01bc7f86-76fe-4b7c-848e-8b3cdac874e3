package com.stageserver.controller;

import com.stageserver.config.MessageConstants;
import com.stageserver.dto.common.APIResponseDto;
import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.dto.mapper.SpecialEventDtoMapper;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.ContentValidatorService;
import com.stageserver.service.MediaStorageService;
import com.stageserver.service.SpecialEventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.util.List;
import java.util.Optional;

import static com.stageserver.service.MediaStorageService.MAX_ALLOWED_FILE_SIZE;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class SpecialEventController {

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Autowired
    private S3Client s3Client;

    @Autowired
    private ContentValidatorService contentValidatorService;

    @Autowired
    private SpecialEventService specialEventService;

    @Autowired
    private MediaStorageService mediaStorageService;

    @Operation(summary = "Create a new Special Event for the profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SpecialEvent created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "profileId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/api/v1/private/special-events")
    public ResponseEntity<APIResponseDto<SpecialEventDto>> createEvent(@RequestParam String profileId, @RequestBody SpecialEventDto specialEventDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!contentValidatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for Special Event by user {}", profileId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_ADD_FAIL);
        }

        log.info("create SpecialEvent requested by user: {} with profileId {}", auth.getName(), profileId);

        if(!specialEventService.checkSpecialEventScheduleAvailability(profileId, specialEventDto)) {
            log.warn("Special Event schedule is not available for profileId: {}", profileId);
            return APIResponseDto.error(HttpStatus.CONFLICT, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        Optional<SpecialEvent> optEvent = specialEventService.createSpecialEvent(profileId, auth.getName(), specialEventDto);
        if (optEvent.isPresent()) {
            SpecialEvent event = optEvent.get();
            log.info("SpecialEvent created successfully with specialEventId: {}", event.getSpecialEventId());
            return APIResponseDto.ok(mapper.toSpecialEventDto(event), MessageConstants.MSG_KEY_DATA_ADD_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_ADD_FAIL);
        }
    }

    @Operation(summary = "Read a new Special Event for the specialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SpecialEvent created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/special-events/{specialEventId}")
    public ResponseEntity<APIResponseDto<SpecialEventDto>> readEvent(@PathVariable String specialEventId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!contentValidatorService.guidTokenLengthValidator(specialEventId)) {
            log.warn("Invalid specialEventId {} provided for read Special Event by user {}", specialEventId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        log.info("Read SpecialEvent requested by user: {} with specialEventId {}", auth.getName(), specialEventId);
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        Optional<SpecialEvent> optEvent = specialEventService.readSpecialEvent(specialEventId, auth.getName());
        if (optEvent.isPresent()) {
            SpecialEvent event = optEvent.get();
            log.info("SpecialEvent retrieved successfully with specialEventId: {}", event.getSpecialEventId());
            return APIResponseDto.ok(mapper.toSpecialEventDto(event), MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_READ_FAILED);
        }
    }

    @Operation(summary = "Update a Special Event for the specialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SpecialEvent created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PutMapping("/api/v1/private/special-events/{specialEventId}")
    public ResponseEntity<APIResponseDto<SpecialEventDto>> updateEvent(@PathVariable String specialEventId,
                                                                       @RequestBody SpecialEventDto specialEventDto) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!contentValidatorService.guidTokenLengthValidator(specialEventId)) {
            log.warn("Invalid specialEventId {} provided for update Special Event by user {}", specialEventId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }

        log.info("Update SpecialEvent requested by user: {} with specialEventId {}", auth.getName(), specialEventId);

        if(specialEventService.scheduleChanged(specialEventId, specialEventDto)) {
            if (!specialEventService.checkSpecialEventScheduleAvailability(specialEventDto.getOwnerProfileId(), specialEventDto)) {
                log.warn("Special Event schedule is not available for profileId while updating: {}", specialEventDto.getOwnerProfileId());
                return APIResponseDto.error(HttpStatus.CONFLICT, MessageConstants.ERROR_DATA_ADD_FAIL);
            }
        }

        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        specialEventDto.setSpecialEventId(specialEventId);
        Optional<SpecialEvent> optEvent = specialEventService.updateSpecialEvent(auth.getName(), specialEventDto);
        if (optEvent.isPresent()) {
            SpecialEvent event = optEvent.get();
            log.info("SpecialEvent updated successfully with specialEventId: {}", event.getSpecialEventId());
            return APIResponseDto.ok(mapper.toSpecialEventDto(event), MessageConstants.MSG_KEY_DATA_UPDATE_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_UPDATE_FAILED);
        }
    }

    @Operation(summary = "Delete a Special Event for the specialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SpecialEvent created successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "eventdD is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/special-events/{specialEventId}")
    public ResponseEntity<APIResponseDto<Void>> deleteSpecialEvent(@PathVariable String specialEventId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (!contentValidatorService.guidTokenLengthValidator(specialEventId)) {
            log.warn("Invalid specialEventId {} provided for delete Special Event by user {}", specialEventId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }

        log.info("Delete SpecialEvent requested by user: {} with specialEventId {}", auth.getName(), specialEventId);
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();

        if (specialEventService.deleteSpecialEvent(specialEventId, auth.getName())) {

            log.info("SpecialEvent deleted successfully with specialEventId: {}", specialEventId);
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_DATA_DELETE_SUCCESS);
        } else {
            return APIResponseDto.error(HttpStatus.UNAUTHORIZED, MessageConstants.ERROR_DATA_DELETE_FAILED);
        }
    }

    @Operation(summary = "Get all special Events for the given profileId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SpecialEvents retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "400", description = "profileId is not valid")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/api/v1/private/special-events/all")
    public ResponseEntity<APIResponseDto<Page<SpecialEventDto>>> getAllSpecialEvents( @RequestParam(defaultValue = "0") int page,
                                                                                      @RequestParam(defaultValue = "6") int size,
                                                                                      @RequestParam String profileId) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (!contentValidatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid profileId {} provided for reading all Special Events for user {}", profileId, auth.getName());
            return APIResponseDto.error(HttpStatus.BAD_REQUEST, MessageConstants.ERROR_DATA_READ_FAILED);
        }

        log.info("Get all SpecialEvents requested by user: {} with profileId {}", auth.getName(), profileId);

        Page<SpecialEvent> pageResult = specialEventService.getAllSpecialEvents(profileId, auth.getName(), page, size);
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        List<SpecialEventDto> dtoList = mapper.toSpecialEventDtoList(pageResult.getContent());

        Page<SpecialEventDto> dtoPage = new PageImpl<>(dtoList, pageResult.getPageable(), pageResult.getTotalElements());

        log.info("SpecialEvent list successfully retrieved for profileId: {} ({} results)", profileId, dtoList.size());

        return APIResponseDto.ok(dtoPage, MessageConstants.MSG_KEY_RETRIEVAL_SUCCESS);
    }

    @Operation(summary = "Upload an image file for the given SpecialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file uploaded successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "Event is not found")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping(value = "/api/v1/private/special-events/{profileId}/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<APIResponseDto<String>> uploadSpecialEventImageFile(@PathVariable String profileId,
                                                                       @RequestParam String specialEventId,
                                                                       @RequestPart MultipartFile file) {
        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(!contentValidatorService.guidTokenLengthValidator(specialEventId)) {
            log.warn("Invalid specialEventId provided for upload ImageFile: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if (file.getSize() >= MAX_ALLOWED_FILE_SIZE) {
            log.warn("SpecialEvent Image file size exceeds the allowed limit of 10MB");
            return APIResponseDto.error(HttpStatus.BANDWIDTH_LIMIT_EXCEEDED);
        }

        String originalFilename = file.getOriginalFilename();
        if(originalFilename != null) {
            log.info("Uploading image file, {} for specialEventId: {}", file.getOriginalFilename(), profileId);
            String result = mediaStorageService.storeSpecialEventImageFile(principal.getUsername(), file, profileId, specialEventId);
            if (result != null) {
                return APIResponseDto.ok(result, MessageConstants.MSG_KEY_FILE_UPLOADED);
            } else {
                return APIResponseDto.error(HttpStatus.BAD_REQUEST);
            }
        }
        log.warn("SpecialEvent Image file name is null");
        return APIResponseDto.error(HttpStatus.BAD_REQUEST);
    }

    @Operation(summary = "Return an SpecialEvent image file for the given specialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image file retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found")
    })
    @GetMapping("/api/v1/public/special-events/{specialEventId}/image/{imageName}")
    public ResponseEntity<Resource> getImage(@PathVariable String specialEventId,
                                             @PathVariable String imageName) {
        if (!contentValidatorService.guidTokenLengthValidator(specialEventId)) {
            log.warn("Invalid specialEventId provided for getImage File: {}", specialEventId);
            return ResponseEntity.badRequest().build();
        }

        if (!contentValidatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for getImage: {}", imageName);
            return ResponseEntity.badRequest().build();
        }

        try {
            String s3Key = String.format("images/%s/se/%s", specialEventId, imageName);
            log.info("Fetching special event image from S3: {}", s3Key);

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build();

            ResponseBytes<GetObjectResponse> s3Object = s3Client.getObjectAsBytes(getObjectRequest);
            byte[] imageBytes = s3Object.asByteArray();
            Resource resource = new ByteArrayResource(imageBytes);

            String contentType = s3Object.response().contentType();
            MediaType mediaType = contentType != null
                    ? MediaType.parseMediaType(contentType)
                    : determineMediaType(imageName); // fallback to extension-based check

            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .contentLength(imageBytes.length)
                    .body(resource);

        } catch (NoSuchKeyException e) {
            log.warn("Special event image not found in S3 for key: images/{}/se/{}", specialEventId, imageName);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error fetching special event image from S3", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Method to determine media type based on file extension
    private MediaType determineMediaType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> MediaType.IMAGE_JPEG;
            case "png" -> MediaType.IMAGE_PNG;
            case "gif" -> MediaType.IMAGE_GIF;
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }

    @Operation(summary = "Delete the image file for the given specialEventId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deleted successfully"),
            @ApiResponse(responseCode = "401", description = "User is not authorized - need to login"),
            @ApiResponse(responseCode = "404", description = "profile is not found"),
            @ApiResponse(responseCode = "503", description = "Service Unavailable")
    })
    @SecurityRequirement(name = "bearerAuth")
    @DeleteMapping("/api/v1/private/special-events/{profileId}/image/{imageName}")
    public ResponseEntity<APIResponseDto<Void>> deleteSpecialEventImageFile(@PathVariable String profileId,
                                                                            @PathVariable String imageName,
                                                                            @RequestParam String specialEventId) {
        if(!contentValidatorService.guidTokenLengthValidator(profileId)) {
            log.warn("Invalid specialEventId provided for delete Event Image File: {}", profileId);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        if(!contentValidatorService.fileNameValidator(imageName)) {
            log.warn("Invalid image name provided for delete Special Event ImageFile: {}", imageName);
            return APIResponseDto.error(HttpStatus.BAD_REQUEST);
        }

        log.info("Deleting SpecialEvent image file, {}, for specialEventId: {}", imageName, profileId);
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (mediaStorageService.deleteSpecialEventImageFile(imageName, profileId, auth.getName(), specialEventId)) {
            return APIResponseDto.ok(null, MessageConstants.MSG_KEY_FILE_DELETED);
        } else {
            return APIResponseDto.error(HttpStatus.NOT_FOUND);
        }
    }
}
