package com.stageserver.events;

import com.stageserver.config.Constants;
import com.stageserver.model.profile.Profile;
import com.stageserver.service.ProfileService;
import com.stageserver.service.SystemUserService;
import com.stageserver.service.interfaces.I_UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class VirtualActAddedEventListener implements ApplicationListener<VirtualActAddedEvent> {

    private final SystemUserService systemUserService;
    private final ProfileService profileService;
    private final I_UtilityService utilityService;
    private final JavaMailSender mailSender;
    private final SpringTemplateEngine templateEngine;
    private final Constants constants;

    @Override
    public void onApplicationEvent(VirtualActAddedEvent event) {
        String email = event.getContactEmail();

        String claimVirtualActToken = utilityService.generateUniqueToken();
        systemUserService.saveVirtualActClaimToken(event.getProfileId(), email, claimVirtualActToken);

        try {
            Optional<Profile> optProfile = profileService.getProfile(event.getProfileId());
            String actName = optProfile.map(Profile::getProfileName).orElse("Act");
            if(profileService.userEmailAlreadyExists(email)) {
                String claimVirtualActUrl = constants.getFrontEndUrl() + "/verify-virtual-act?token=" + claimVirtualActToken;
                log.info("Claim Virtual Act URL: {}", claimVirtualActUrl);
                sendClaimEmailNotification(email, claimVirtualActToken, constants.getFrontEndUrl(), actName);
            } else {
                String signupUrl = constants.getFrontEndUrl() + "/en/signup";
                log.info("Sign-up URL: {}", signupUrl);
                sendJoinSystemEmail(email, signupUrl, actName);
            }
        } catch (MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendClaimEmailNotification(String email, String token, String url, String actName) throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();
        context.setVariable("token", token);
        context.setVariable("url", url);
        context.setVariable("actName", actName);

        String process = templateEngine.process("virtual_act_added.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Claim Your Account");
        helper.setText(process, true);
        mailSender.send(message);
    }

    public void sendJoinSystemEmail(String email, String url, String actName) throws MessagingException, UnsupportedEncodingException {
        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();
        context.setVariable("url", url);
        context.setVariable("actName", actName);

        String process = templateEngine.process("join_the_system.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Please join StageMinder");
        helper.setText(process, true);
        mailSender.send(message);
    }
}
