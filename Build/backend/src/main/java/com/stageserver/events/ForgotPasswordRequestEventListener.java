package com.stageserver.events;

import com.stageserver.config.Constants;
import com.stageserver.config.MessageConstants;
import com.stageserver.exceptions.TokenAlreadyExistException;
import com.stageserver.model.login.ForgotPasswordToken;
import com.stageserver.model.login.User;
import com.stageserver.repository.ForgotPasswordTokenRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.service.interfaces.I_LoginService;
import com.stageserver.service.interfaces.I_UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ForgotPasswordRequestEventListener  implements ApplicationListener<ForgotPasswordRequestEvent> {

    private final I_LoginService loginService;
    private final I_UtilityService utilityService;
    private final JavaMailSender mailSender;
    private final UserRepository userRepository;
    private final ForgotPasswordTokenRepository forgotPasswordTokenRepository;
    private final Constants constants;
    private final SpringTemplateEngine templateEngine;

    private User newUser;


    @Override
    public void onApplicationEvent(ForgotPasswordRequestEvent event) {
        String email = event.getUserEmail();
        String forgotPasswordToken = utilityService.generateUniqueToken();
        String forgotPasswordResetUrl =  constants.getFrontEndUrl()+ "/reset-password?token=" + forgotPasswordToken;
        Optional<User> user = userRepository.findByEmail(email);
        if(user.isPresent()) {
            newUser = user.get();
            List<ForgotPasswordToken> tokenList = forgotPasswordTokenRepository.findAllTokensForUserByEmail(newUser.getEmail());
            if(!tokenList.isEmpty()) {
                throw new TokenAlreadyExistException(HttpStatus.ALREADY_REPORTED, MessageConstants.getErrorMap().get(MessageConstants.ERROR_FORGOT_PASSWORD_TOKEN_EXISTS));
            }

            loginService.saveUserForgotPasswordToken(user.get(), forgotPasswordToken);
            log.info("reset link for forgot password: {}", forgotPasswordResetUrl);
        }
        try {
            sendForgotPasswordEmail(constants.getFrontEndUrl(), forgotPasswordToken);
        }
        catch(MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendForgotPasswordEmail(String url, String token) throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();

        context.setVariable("token", token);
        context.setVariable("url", url);
        String process = templateEngine.process("reset_password.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(newUser.getEmail());
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Your Request to Reset Password");
        helper.setText(process, true);
        mailSender.send(message);
    }


}
