package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.SupportedRegionsDto;
import com.stageserver.model.supported.SupportedRegions;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class SupportedRegionsDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SupportedRegions toSupportedRegions(SupportedRegionsDto dto) {
        return modelMapper.map(dto, SupportedRegions.class);
    }

    public SupportedRegionsDto toSupportedRegionsDto(SupportedRegions request) {
        return modelMapper.map(request, SupportedRegionsDto.class);
    }
}

