package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.ModifiedGoodsAndServicesDto;
import com.stageserver.model.contract.ModifiedGoodsAndServices;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ModifiedGoodsAndServicesDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public ModifiedGoodsAndServicesDto toModifiedGoodsAndServicesDto(ModifiedGoodsAndServices data) {
        return modelMapper.map(data, ModifiedGoodsAndServicesDto.class);
    }

    public ModifiedGoodsAndServices toModifiedGoodsAndServices(ModifiedGoodsAndServicesDto dto) {
        return modelMapper.map(dto, ModifiedGoodsAndServices.class);
    }
}
