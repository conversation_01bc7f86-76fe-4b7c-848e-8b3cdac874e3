package com.stageserver.dto.contracts;

import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.payment.PaymentDisplayStatusDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.contract.ContractState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContractDetailsDto {

    public enum ContractParty {
        ACT,
        VENUE,
        USER,
        UNKNOWN
    }
    private String contractId;

    private String actProfileId;

    private String venueProfileId;

    private String actProfileName;

    private String venueProfileName;

    private LocationDto venueLocation;

    private ScheduleTimeDto scheduleTime;

    private GoodsAndServicesDto goodsAndServices;

    /* Rider information */
    private ActRiderNotesDto actRiderNotes;
    
    private ActRiderChangesDto actRiderChanges;

    private VenueRiderNotesDto venueRiderNotes;

    private VenueRiderChangesDto venueRiderChanges;

    private FinePrintData finePrint;

    /* user info */
    private UserInfoDto purchaserInfo;

    private UserInfoDto otherPartyInfo;

    /* reviews and ratings */
    private int numberOfVenueReviews;

    private int numberOfActReviews;

    private double actRating;

    private double venueRating;

    /* booking types */
    private ContractParty bookingParty;

    private ContractParty otherParty;

    private String originatingUser;

    private List<String> actProfileImageUrls;

    private List<String> venueProfileImageUrls;

    private ContractState contractState;

    private List<String> possibleActions;

    private ZonedDateTime timeStamp;

    private String eventId;

    private boolean displayFeedback;

    private boolean updateFeedback;

    private String feedbackId;

    private PaymentDisplayStatusDto paymentStatus;

    private boolean editable;
}
