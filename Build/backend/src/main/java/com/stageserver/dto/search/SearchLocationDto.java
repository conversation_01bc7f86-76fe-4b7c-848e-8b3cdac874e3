package com.stageserver.dto.search;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

@Setter
@Getter
@NoArgsConstructor
@Component
public class SearchLocationDto {

    private String countryName;

    private String stateName;

    private String cityName;

    private int distance;

    @Override
    public String toString() {
        return "SearchData{" +
                "countryName='" + countryName + '\'' +
                ", stateName='" + stateName + '\'' +
                ", cityName='" + cityName + '\'' +
                ", distance=" + distance +
                '}';
    }
}
