package com.stageserver.dto.mapper;


import com.stageserver.dto.profile.ActSkillsDto;
import com.stageserver.model.profile.ActSkills;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ActSkillsDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public ActSkills toActSkills(ActSkillsDto dto) {
        return modelMapper.map(dto, ActSkills.class);
    }

    public ActSkillsDto toActSkillsDto(ActSkills skills) {
       return modelMapper.map(skills, ActSkillsDto.class);
    }

}
