package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.VirtualContactDto;
import com.stageserver.model.profile.VirtualContact;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class VirtualContactDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public VirtualContactDto toVirtualContactDto(VirtualContact contact) {
        return modelMapper.map(contact, VirtualContactDto.class);
    }

    public VirtualContact toVirtualContact(VirtualContactDto dto) {
        return modelMapper.map(dto, VirtualContact.class);
    }
}
