package com.stageserver.dto.profile;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProfileInfoDto {

    private String bio;

    private boolean suitableForChildren;

    private boolean suitableForAdultsOnly;

    private List<String> socialMediaLinks;

    private WeeklyWorkingHoursDto weeklyWorkingHours;
}
