package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.ProfilePaymentsDto;
import com.stageserver.model.profile.ProfilePayments;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class ProfilePaymentsDtoMapper {
    private ModelMapper mapper = new ModelMapper();

    public ProfilePayments toActPayments(ProfilePaymentsDto profilePaymentsDto) {
        return mapper.map(profilePaymentsDto, ProfilePayments.class);
    }

    public ProfilePaymentsDto toActPaymentsDto(ProfilePayments profilePayments) {
        return mapper.map(profilePayments, ProfilePaymentsDto.class);
    }

    public ProfilePaymentsDto.ChargingType toChargingTypeDto(ProfilePayments.ChargingType chargingType) {
       if(chargingType != null) {
           return switch (chargingType) {
               case HOURLY -> ProfilePaymentsDto.ChargingType.HOURLY;
               case EVENT -> ProfilePaymentsDto.ChargingType.EVENT;
               default -> null;
           };

       }
       return null;
    }
}
