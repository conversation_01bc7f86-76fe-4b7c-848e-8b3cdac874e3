package com.stageserver.config;


import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition( info = @Info( title = "Open API Documentation for BackEnd",
license = @License(name = "Strictly Confidential")))
@SecurityScheme(name = "bearerAuth", description = "JWT Auth Description", scheme = "bearer", type = SecuritySchemeType.HTTP,
bearerFormat = "JWT", in = SecuritySchemeIn.HEADER)
public class OpenAPIConfig
{
}
