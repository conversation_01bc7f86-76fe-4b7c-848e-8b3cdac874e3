package com.stageserver.model.contract;

import com.stageserver.model.event.Event;
import com.stageserver.model.location.Location;
import com.stageserver.model.payment.PaymentState;
import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@Node
public class Contract {

    @Id
    @GeneratedValue
    private String elementId;

    private String contractId;

    private String originatingUser;

    private String receivingUser;

    private boolean usingPrivateVenue;

    @Relationship(type = "PRIVATE_VENUE_LOCATION", direction = Relationship.Direction.OUTGOING)
    private Location privateVenueLocation;

    private String venueProfileId;

    private String actProfileId;

    private GoodsAndServices goodsAndServices;

    private ActRiderNotes actRiderNotes;

    private ActRiderChanges actRiderChanges;

    private VenueRiderNotes venueRiderNotes;

    private VenueRiderChanges venueRiderChanges;

    @Relationship(type = "HAS_SCHEDULE", direction = Relationship.Direction.OUTGOING)
    private ScheduleTime scheduleTime;

    private ContractState contractState;

    private boolean scheduleMoved = false;

    private ZonedDateTime timeStamp;

    @Relationship(value = "HAS_EVENTS", direction = Relationship.Direction.OUTGOING)
    private Event event;

    private PaymentState paymentState;

    private String negotiateOriginatingUser;
}
