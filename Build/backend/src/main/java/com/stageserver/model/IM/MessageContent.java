package com.stageserver.model.IM;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Node
public class MessageContent {

    @Id
    @GeneratedValue
    private String elementId;

    public enum MessageType {
        GENERIC_MESSAGE,
        BOOKING_REQUEST,
        FEEDBACK_MESSAGE
    }

    private MessageType messageType;

    private String contractId;

    private String feedbackId;

    private String contractState;

    private String message;

    private String contractMessageJson;

    private String feedbackMessageJson;

}
