package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class ProfilePayments {

    public enum ChargingType {
        HOURLY,
        EVENT
    }

    @Id
    @GeneratedValue
    private String elementId;

    private String currency;

    private int typicalPrice;

    private int minimumPrice;

    private ChargingType minPriceChargingType;

    private ChargingType typicalPriceChargingType;

    private List<String> acceptablePaymentMethods;

    private boolean forRent;
}
