package com.stageserver.model.event;

import com.stageserver.dto.profile.ProfileMediaDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.profile.ProfileMedia;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@Setter
@Getter
@Node
public class EventMainInfo {
    @Id
    @GeneratedValue
    private String elementId;

    private List<String> tags;

    private String eventName;

    private String aboutEvent;

    private List<String> socialMediaUrls;
}
