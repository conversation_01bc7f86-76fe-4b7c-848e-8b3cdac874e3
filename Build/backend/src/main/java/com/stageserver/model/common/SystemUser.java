package com.stageserver.model.common;

import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.VirtualActClaimToken;
import com.stageserver.model.profile.VirtualVenueClaimToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Node
public class SystemUser {

    @Id
    @GeneratedValue
    private String elementId;

    @Relationship(value = "HAS_VIRTUAL_PROFILES", direction = Relationship.Direction.OUTGOING)
    private List<Profile> profileList = new ArrayList<>();

    @Relationship(value = "HAS_VIRTUAL_ACT_CLAIM_TOKENS", direction = Relationship.Direction.OUTGOING)
    private List<VirtualActClaimToken> virtualActClaimTokenList = new ArrayList<>();

    @Relationship(value = "HAS_VIRTUAL_VENUE_CLAIM_TOKENS", direction = Relationship.Direction.OUTGOING)
    private List<VirtualVenueClaimToken> virtualVenueClaimTokenList = new ArrayList<>();
}
