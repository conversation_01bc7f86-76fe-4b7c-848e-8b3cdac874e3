package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.ZonedDateTime;

@NoArgsConstructor
@Getter
@Setter
@Node
public class RiderDetails {
    @Id
    @GeneratedValue
    private String elementId;

    private String riderDocument;

    private ZonedDateTime uploadedTime;
}
