package com.stageserver.model.contract;

import com.stageserver.dto.contracts.GoodsAndServicesDto;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
@Node
public class GoodsAndServices {

    public enum ActOrVenue {
        ACT,
        VENUE
    }

    public enum EquipmentProvider {
        PURCHASER,
        PERFORMER,
        NOT_APPLICABLE
    }

    public enum PaymentType {
        FLAT_RATE,
        DOOR_GIG,
        EXPOSURE_GIG
    }

    @Id
    @GeneratedValue
    private String elementId;

    private String performersRole;

    private ZonedDateTime startDate;

    private double durationInHours;

    private ZonedDateTime loadingTime;

    private PaymentType paymentType;

    private String flatRateCurrency;

    private double flatRateAmount;

    private double flatRatePercentage;

    private double doorGigEntryFee;

    private int venueCapacity;

    private ActOrVenue doorManagedBy;

    private ActOrVenue doorGigPaidBy;

    private ActOrVenue payableTo;

    private double maximumPercentage;

    private int guaranteedMaximum;

    private double exposureGigFee;

    private String exposureGigCurrency;

    private EquipmentProvider equipmentProvider;

    private boolean mealsProvidedByPurchaser;

    private  boolean accommodationProvided;

    private boolean merchandiseSalesAllowed;

    private boolean performerMemberOfUnion;

    private String message;

    private boolean equalsOrNull(Object o1, Object o2) {
        return (o1 == null && o2 == null) || (o1 != null && o1.equals(o2));
    }

    public ModifiedGoodsAndServices compareWith(GoodsAndServices other) {
        ModifiedGoodsAndServices result = new ModifiedGoodsAndServices();

        // Compare fields one by one
        if (!equalsOrNull(performersRole, other.performersRole)) {
            result.setPerformersRoleModified(true);
        }
        if (!equalsOrNull(startDate, other.startDate)) {
           result.setStartDateModified(true);
        }
        if (durationInHours != other.durationInHours) {
            result.setDurationInHoursModified(true);
        }
        if (!equalsOrNull(loadingTime, other.loadingTime)) {
            result.setLoadingTimeModified(true);
        }
        if (paymentType != other.paymentType) {
            result.setPaymentTypeModified(true);
        }
        if (!equalsOrNull(flatRateCurrency, other.flatRateCurrency)) {
            result.setFlatRateCurrencyModified(true);
        }
        if (flatRateAmount != other.flatRateAmount) {
            result.setFlatRateAmountModified(true);
        }
        if (flatRatePercentage != other.flatRatePercentage) {
            result.setFlatRatePercentageModified(true);
        }
        if (doorGigEntryFee != other.doorGigEntryFee) {
            result.setDoorGigEntryFeeModified(true);
        }
        if (venueCapacity != other.venueCapacity) {
            result.setVenueCapacityModified(true);
        }
        if (doorManagedBy != other.doorManagedBy) {
            result.setDoorManagedByModified(true);
        }
        if (doorGigPaidBy != other.doorGigPaidBy) {
            result.setDoorGigPaidByModified(true);
        }
        if (payableTo != other.payableTo) {
            result.setPayableToModified(true);
        }
        if (maximumPercentage != other.maximumPercentage) {
            result.setMaximumPercentageModified(true);
        }
        if (guaranteedMaximum != other.guaranteedMaximum) {
            result.setGuaranteedMaximumModified(true);
        }
        if (exposureGigFee != other.exposureGigFee) {
            result.setExposureGigFeeModified(true);
        }
        if (!equalsOrNull(exposureGigCurrency, other.exposureGigCurrency)) {
            result.setExposureGigCurrencyModified(true);
        }
        if (equipmentProvider != other.equipmentProvider) {
            result.setEquipmentProviderModified(true);
        }
        if (mealsProvidedByPurchaser != other.mealsProvidedByPurchaser) {
            result.setMealsProvidedByPurchaserModified(true);
        }
        if (accommodationProvided != other.accommodationProvided) {
            result.setAccommodationProvidedModified(true);
        }
        if (merchandiseSalesAllowed != other.merchandiseSalesAllowed) {
            result.setMerchandiseSalesAllowedModified(true);
        }
        if (performerMemberOfUnion != other.performerMemberOfUnion) {
            result.setPerformerMemberOfUnionModified(true);
        }
        if (!equalsOrNull(message, other.message)) {
            result.setMessageModified(true);
        }

        return result;
    }
}
