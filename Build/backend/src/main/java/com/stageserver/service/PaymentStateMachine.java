package com.stageserver.service;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.payment.PaymentAction;
import com.stageserver.model.payment.PaymentState;
import com.stageserver.repository.contract.ContractRepository;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
@Slf4j
@Service
public class PaymentStateMachine {

    /* Here the payment state machine only have one state transition */
    /* However, preserving the state machine structure for future enhancements */
    /* The previous implementation had multiple state transitions because both party had to pay */
    @Autowired
    private ContractRepository contractRepository;

    private PaymentState currentState;

    private final Map<PaymentState, Map<PaymentAction, PaymentState>> transitions = new EnumMap<>(PaymentState.class);

    public PaymentStateMachine() {
        // Initialize the state transition map
        initializeTransitions();

        // Set the initial state
        this.currentState = PaymentState.NO_PAYMENT;
    }

    private void initializeTransitions() {
        /* Transitions from CREATED state */
        Map<PaymentAction, PaymentState> initTransitions = new EnumMap<>(PaymentAction.class);
        initTransitions.put(PaymentAction.PAYER_PAYS, PaymentState.PAYMENT_MADE);

        transitions.put(PaymentState.NO_PAYMENT, initTransitions);
    }

    public boolean sendEvent(PaymentAction action, String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);

        if (optContract.isEmpty()) {
            log.warn("sendEvent:: Contract {} is not present in the system", contractId);
            return false;
        }

        currentState = optContract.get().getPaymentState();
        Map<PaymentAction, PaymentState> possibleTransitions = transitions.get(currentState);
        if (possibleTransitions != null && possibleTransitions.containsKey(action)) {
            currentState = possibleTransitions.get(action);
        } else {
            log.warn("Invalid PaymentAction {} for state {} in contract {}", action , currentState, contractId);
            return false;
        }
        return true;
    }
}
