package com.stageserver.service;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

@Component
public class ScheduledTaskRegistry {
    //Simple class to hold the ScheduledTasks so that we can cancel them later
    private final Map<String, ScheduledFuture<?>> tasks = new ConcurrentHashMap<>();

    public void register(String eventId, ScheduledFuture<?> future) {
        tasks.put(eventId, future);
    }

    public ScheduledFuture<?> unregister(String eventId) {
        return tasks.remove(eventId);
    }

    public ScheduledFuture<?> getTask(String eventId) {
        return tasks.get(eventId);
    }
}
