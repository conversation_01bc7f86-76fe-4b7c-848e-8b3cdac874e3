package com.stageserver.service;

import com.stageserver.model.login.User;
import com.stageserver.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service to initialize test user data when test mode is enabled.
 * This ensures the default test user exists in the database.
 */
@Slf4j
@Service
public class TestUserInitializationService implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Value("${stage-server.test-mode.enabled:false}")
    private boolean testModeEnabled;

    @Value("${stage-server.test-mode.default-user:<EMAIL>}")
    private String defaultTestUser;

    @Value("${stage-server.test-mode.default-role:USER}")
    private String defaultTestRole;

    @Override
    public void run(String... args) throws Exception {
        if (testModeEnabled) {
            initializeTestUser();
        }
    }

    private void initializeTestUser() {
        try {
            Optional<User> existingUser = userRepository.findByEmail(defaultTestUser);
            
            if (existingUser.isEmpty()) {
                log.info("Test mode enabled - creating default test user: {}", defaultTestUser);
                
                User testUser = new User();
                testUser.setEmail(defaultTestUser);
                testUser.setFirstName("Test");
                testUser.setLastName("User");
                testUser.setPassword(passwordEncoder.encode("testpassword123")); // Default password
                testUser.setRole(defaultTestRole);
                testUser.setEnabled(true); // Enable the user
                testUser.setTwoFaEnabled(false); // Disable 2FA for testing
                testUser.setSocialLoginUser(false);
                
                userRepository.save(testUser);
                log.info("Test user {} created successfully with role {}", defaultTestUser, defaultTestRole);
            } else {
                log.info("Test user {} already exists", defaultTestUser);
                
                // Ensure the test user is enabled and has correct settings
                User user = existingUser.get();
                boolean needsUpdate = false;
                
                if (!user.isEnabled()) {
                    user.setEnabled(true);
                    needsUpdate = true;
                    log.info("Enabled test user {}", defaultTestUser);
                }
                
                if (user.isTwoFaEnabled()) {
                    user.setTwoFaEnabled(false);
                    needsUpdate = true;
                    log.info("Disabled 2FA for test user {}", defaultTestUser);
                }
                
                if (!defaultTestRole.equals(user.getRole())) {
                    user.setRole(defaultTestRole);
                    needsUpdate = true;
                    log.info("Updated role for test user {} to {}", defaultTestUser, defaultTestRole);
                }
                
                if (needsUpdate) {
                    userRepository.save(user);
                    log.info("Test user {} updated successfully", defaultTestUser);
                }
            }
        } catch (Exception e) {
            log.error("Error initializing test user: {}", e.getMessage(), e);
        }
    }
}
