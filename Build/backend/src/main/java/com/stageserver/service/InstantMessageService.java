package com.stageserver.service;

import com.stageserver.dto.IM.ContractContentDto;
import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.mapper.InstantMessageDtoMapper;
import com.stageserver.dto.payment.PaymentDisplayStatusDto;
import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.IM.MessageContent;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractAction;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.repository.*;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.I_InstantMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class InstantMessageService implements I_InstantMessageService {


    @Autowired
    private SimpMessagingTemplate simpleMessagingTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private InstantMessageRepository instantMessageRepository;

    @Autowired
    private MessageContentRepository messageContentRepository;

    @Autowired
    private ContractService contractService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ContractServiceUtil contractServiceUtil;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private FeedbackService feedbackService;

    @Autowired
    private FeedbackMsgRepository feedbackMsgRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ContractStateService contractStateService;

    @Transactional
    public InstantMessage sendMessage(String sender, String receiver, String content) {

        Optional<User> optSenderUser = userRepository.findByEmail(sender);
        Optional<User> optReceiverUser = userRepository.findByEmail(receiver);

        if (optSenderUser.isPresent() && optReceiverUser.isPresent()) {
            User senderUser = optSenderUser.get();
            User receiverUser = optReceiverUser.get();

            InstantMessage message = new InstantMessage();
            message.setMessageId("IM-" + System.currentTimeMillis());
            message.setSender(sender);
            message.setReceiver(receiver);

            //   message.setContent(content);
            message.setTimestamp(LocalDateTime.now());
            message.setSeen(false);

            senderUser.getMessageBox().getSentMessages().add(message);
            receiverUser.getMessageBox().getReceivedMessages().add(message);
            userRepository.save(senderUser);
            userRepository.save(receiverUser);

            return instantMessageRepository.save(message);
        } else {
            log.warn("Sender or receiver not in the system to send message");
            return null;
        }
    }

    @Override
    @Transactional
    public InstantMessage setupIMMessageForUser(InstantMessage message) {

        Optional<User> optSenderUser = userRepository.findByEmail(message.getSender());
        Optional<User> optReceiverUser = userRepository.findByEmail(message.getReceiver());

        if (optSenderUser.isPresent() && optReceiverUser.isPresent()) {
            User senderUser = optSenderUser.get();
            User receiverUser = optReceiverUser.get();

            message.setMessageId("IM-" + System.currentTimeMillis());
            message.setTimestamp(LocalDateTime.now());
            message.setSeen(false);
            senderUser.getMessageBox().getSentMessages().add(message);
            receiverUser.getMessageBox().getReceivedMessages().add(message);
            userRepository.save(senderUser);
            userRepository.save(receiverUser);
            instantMessageRepository.save(message);
            return message;
        } else {
            log.warn("Sender or receiver not in the system");
            return null;
        }
    }

    @Override
    @Transactional
    public InstantMessage setupIMMessageToVirtualProfile(InstantMessage message, String receiverProfileId) {

        Optional<User> optSenderUser = userRepository.findByEmail(message.getSender());

        if (optSenderUser.isPresent()) {
            User senderUser = optSenderUser.get();
            Optional<Profile> optProfile = profileRepository.findByProfileId(receiverProfileId);
            if(optProfile.isPresent()) {
                Profile profile = optProfile.get();
                message.setMessageId("IM-" + System.currentTimeMillis());
                message.setTimestamp(LocalDateTime.now());
                message.setSeen(false);
                message.setDismissed(false);
                message.getContent().setContractState(ContractState.RECEIVED.toString());
                senderUser.getMessageBox().getSentMessages().add(message);
                profile.getMessageBox().getReceivedMessages().add(message);
                userRepository.save(senderUser);
                profileRepository.save(profile);
                instantMessageRepository.save(message);
                log.info("Message {} saved in virtual profile: {}", message.getMessageId(), profile.getProfileName());
                return message;
            }
        }
        log.warn("Sender or receiver not in the system for virtual booking");
        return null;
    }

    @Override
    @Transactional
    public InstantMessage setupIMMessageFromVirtualProfile(InstantMessage message, String senderProfileId) {

        Optional<User> optReceiverUser = userRepository.findByEmail(message.getReceiver());

        if (optReceiverUser.isPresent()) {
            User receiveUser = optReceiverUser.get();
            Optional<Profile> optProfile = profileRepository.findByProfileId(senderProfileId);
            if(optProfile.isPresent()) {
                Profile profile = optProfile.get();
                message.setMessageId("IM-" + System.currentTimeMillis());
                message.setTimestamp(LocalDateTime.now());
                message.setSeen(false);
                receiveUser.getMessageBox().getReceivedMessages().add(message);
                profile.getMessageBox().getSentMessages().add(message);
                userRepository.save(receiveUser);
                profileRepository.save(profile);
                instantMessageRepository.save(message);
                return message;
            }
        }
        log.warn("Sender or receiver not in the system for canceling virtual booking");
        return null;
    }

    private boolean isVirtualBooking(ContractContentDto contractContentDto) {
        String profileId = "";
        ContractDetailsDto.ContractParty otherParty = contractContentDto.getOtherParty();
        Optional<Contract> optContract = contractRepository.findByContractId(contractContentDto.getContractId());
        if(optContract.isPresent()) {
            if (otherParty == ContractDetailsDto.ContractParty.ACT) {
                profileId = optContract.get().getActProfileId();
            } else if (otherParty == ContractDetailsDto.ContractParty.VENUE) {
                profileId = optContract.get().getVenueProfileId();
            }
            Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
            if(optProfile.isPresent()) {
                Profile profile = optProfile.get();
                if(profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE || profile.getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE) {
                    contractContentDto.setActionString("StageMinder has cancelled the Booking of " + profile.getProfileName() + " as recipient did not respond");
                    return true;
                }
            }
        }
        return false;
    }

    private List<InstantMessageDto> populateMessageList(List<InstantMessage> messageList, boolean sentMessages) {
        List<InstantMessageDto> resultList = new ArrayList<>();
        for (InstantMessage message : messageList) {
            MessageContent content = messageContentRepository.getMessageContentForMessageId(message.getMessageId());

            if (content != null) {
                if (content.getMessageType() == MessageContent.MessageType.BOOKING_REQUEST) {
                    if (!message.isDismissed()) {
                        InstantMessageDto dto = new InstantMessageDto();
                        dto.setMessageId(message.getMessageId());
                        dto.setSender(message.getSender());
                        dto.setReceiver(message.getReceiver());
                        dto.setTimestamp(message.getTimestamp());
                        dto.setSeen(message.isSeen());
                        dto.setDismissed(message.isDismissed());
                        dto.setContent(contractServiceUtil.populateContractMessageContentFromJson(content.getContractMessageJson()));
                        // Received Messages should not have a state of SENT, change to RECEIVED
                        if(!sentMessages) {
                            if (dto.getContent().getContractContentDto().getContractState() == ContractState.SENT) {
                                dto.getContent().getContractContentDto().setContractState(ContractState.RECEIVED);
                            }
                        }

                        if((dto.getContent().getContractContentDto().getContractState() == ContractState.CANCELLED) &&
                                (isVirtualBooking(dto.getContent().getContractContentDto()))) {
                            log.info("Updated Virtual Booking cancelled msg action String");
                        }
                        else {
                            dto.getContent().getContractContentDto().setActionString(generateActionString(dto, message.getReceiver()));
                        }
                        resultList.add(dto);
                    }
                } else if (content.getMessageType() == MessageContent.MessageType.GENERIC_MESSAGE) {
                    if (!message.isDismissed()) {
                        InstantMessageDto dto = new InstantMessageDto();
                        dto.setMessageId(message.getMessageId());
                        dto.setSender(message.getSender());
                        dto.setReceiver(message.getReceiver());
                        dto.setTimestamp(message.getTimestamp());
                        dto.setSeen(message.isSeen());
                        dto.setContent(contractServiceUtil.populateGenericMessageContentFromJson(content.getContractMessageJson(), message.getSender(), message.getReceiver()));
                        resultList.add(dto);
                    }
                }
                else if( content.getMessageType() == MessageContent.MessageType.FEEDBACK_MESSAGE) {
                    if (!message.isDismissed()) {
                        InstantMessageDto dto = new InstantMessageDto();
                        dto.setMessageId(message.getMessageId());
                        dto.setSender(message.getSender());
                        dto.setReceiver(message.getReceiver());
                        dto.setTimestamp(message.getTimestamp());
                        dto.setSeen(message.isSeen());
                        if(content.getFeedbackMessageJson() != null) {
                            dto.setContent(feedbackService.populateFeedbackMessageContentFromJson(content.getFeedbackMessageJson()));
                        }
                        resultList.add(dto);
                    }
                }
            }
        }
        return resultList;
    }


    public List<InstantMessageDto> getReceivedMessagesForUser(String email) {
        List<InstantMessage> instantMessageList = instantMessageRepository.getReceivedMessagesForUser(email);
        return populateMessageList(instantMessageList, false);
    }

    public List<InstantMessageDto> getSentMessagesForUser(String email) {
        List<InstantMessage> instantMessageList = instantMessageRepository.getSentMessagesForUser(email);
        return populateMessageList(instantMessageList, true);
    }

    @Override
    public List<InstantMessageDto> getReceivedMessagesForProfile(String email, String profileId) {
        List<InstantMessage> instantMessageList = instantMessageRepository.getReceivedMessagesForProfile(email, profileId);
        return populateMessageList(instantMessageList, false);
    }

    @Override
    public List<InstantMessageDto> getSentMessagesForProfile(String email, String profileId) {
        List<InstantMessage> instantMessageList = instantMessageRepository.getSentMessagesForProfile(email, profileId);
        return populateMessageList(instantMessageList, true);
    }

    @Override
    @Transactional
    public boolean setMessagesAsSeen(String email, String messageId) {
        Optional<InstantMessage> optInstantMessage = instantMessageRepository.getMessageForMessageId(messageId);
        if (optInstantMessage.isPresent()) {
            InstantMessage message = optInstantMessage.get();
            MessageContent content = messageContentRepository.getMessageContentForMessageId(message.getMessageId());
            // message.setDismissed(true);
            message.setContent(content);
            message.setSeen(true);
            instantMessageRepository.save(message);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteNotification(String email, String messageId) {
        Optional<InstantMessage> optInstantMessage = instantMessageRepository.getMessageForMessageId(messageId);
        if (optInstantMessage.isPresent()) {
            InstantMessage message = optInstantMessage.get();
            MessageContent content = messageContentRepository.getMessageContentForMessageId(message.getMessageId());
            message.setDismissed(true);
            message.setSeen(true);
            message.setContent(content);
            instantMessageRepository.save(message);
            return true;
        }
        return false;
    }

    @Override
    public Optional<ContractDetailsDto> getContractDetailsForMessageId(String messageId, String email) {
        MessageContent content = messageContentRepository.getMessageContentForMessageId(messageId);
        if (content != null) {
            String json = content.getContractMessageJson();
            if ((json != null) && (!json.isEmpty())) {
                ContractDetailsDto contractDetails = contractService.convertJsonToObject(json);
                if(shouldDisablePossibleActions(contractDetails, messageId, email)) {
                    List<String> disabledAction = new ArrayList<>();
                    disabledAction.add(ContractAction.DISABLED.name());
                    contractDetails.setPossibleActions(disabledAction);
                }
                else if((contractDetails.getContractState() == ContractState.SENT) || (contractDetails.getContractState() == ContractState.RECEIVED)) {
                    // We are only sending for the receiver of the message, so change SENT to RECEIVED
                    contractDetails.setContractState(ContractState.RECEIVED);
                    contractDetails.setPossibleActions(contractService.getPossibleActions(email, contractDetails.getContractId(), ContractState.RECEIVED));
                }
                else if(contractDetails.getContractState() == ContractState.NEGOTIATING) {
                    contractDetails.setPossibleActions(contractService.getPossibleActions(email, contractDetails.getContractId(), ContractState.NEGOTIATING));
                }
                Optional<Contract> optContract = contractRepository.findByContractId(contractDetails.getContractId());
                if(optContract.isPresent()) {
                    Contract contract = optContract.get();
                    PaymentDisplayStatusDto paymentStatus = contractService.getPaymentDisplayStatus(contractDetails, contract.getPaymentState());
                    contractDetails.setPaymentStatus(paymentStatus);
                }
                return Optional.of(contractDetails);
            }
        }
        return Optional.empty();
    }

    private boolean shouldDisablePossibleActions(ContractDetailsDto contractDetails, String messageId, String email) {
        String contractId = contractDetails.getContractId();
        Optional<ContractState> optCurrentState = contractService.getCurrentContractState(contractId);
        if(optCurrentState.isPresent()) {
            ContractState currentState = optCurrentState.get();

            // We need to check if the contract state has changed since the message was sent
            // But for RECEIVED state, we change it to SENT.
            if((contractDetails.getContractState() == ContractState.SENT) && ((currentState != ContractState.SENT && currentState != ContractState.RECEIVED))) {
                return true;
            }
            if((contractDetails.getContractState() != ContractState.SENT ) && (currentState != contractDetails.getContractState())) {
                return true;
            }
            // This may not need it as we should have done it already
            if(contractDetails.getContractState() == ContractState.SENT) {
                contractDetails.setContractState(ContractState.RECEIVED);
            }
            if(currentState == ContractState.NEGOTIATING) {
                // Then we must check if we have more than one message in the negotiation
                Optional<InstantMessage> optNegotiateMessage = instantMessageRepository.getLastNegotiateMessage(contractId, email);
                if(optNegotiateMessage.isPresent()) {
                    InstantMessage negotiateMessage = optNegotiateMessage.get();
                    return !negotiateMessage.getMessageId().equals(messageId);
                }
            }
        }
        return false;
    }

    public String generateActionString(InstantMessageDto instantMessageDto,String email) {
        String resultString = "";
        String contractId = instantMessageDto.getContent().getContractContentDto().getContractId();

        ContractState contractState = instantMessageDto.getContent().getContractContentDto().getContractState();

        // sender can be the booking party or the other party
        String sender = instantMessageDto.getSender();
        String sendingEntity = "";
        String receivingEntity = "";
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            if((contract.getActProfileId() != null) && (!contract.getActProfileId().isEmpty())) {
                if(profileService.isMyProfile(email, contract.getActProfileId())) {
                    //receiver is the Act
                    receivingEntity =  instantMessageDto.getContent().getContractContentDto().getActProfileName();
                }
                else {
                    //Sender is the Act
                    sendingEntity =  instantMessageDto.getContent().getContractContentDto().getActProfileName();
                }
            }
            if((contract.getVenueProfileId() != null) && (!contract.getVenueProfileId().isEmpty())) {
                if(profileService.isMyProfile(email, contract.getVenueProfileId())) {
                    //receiver is the Venue
                    receivingEntity = instantMessageDto.getContent().getContractContentDto().getVenueProfileName();
                }
                else {
                    //Sender is the Venue
                    sendingEntity = instantMessageDto.getContent().getContractContentDto().getVenueProfileName();
                }
            }

            if(sendingEntity.isEmpty()) {
                //Sender is the user
                sendingEntity = instantMessageDto.getContent().getContractContentDto().getUserName();
            }
            if(receivingEntity.isEmpty()) {
                //Receiver is the user
                receivingEntity = instantMessageDto.getContent().getContractContentDto().getUserName();
            }

            resultString = switch (contractState) {
                case RECEIVED -> receivingEntity + " receives a request from " + sendingEntity;
                case NEGOTIATING -> sendingEntity + " is negotiating with " + receivingEntity;
                case CONFIRMED -> sendingEntity + " accepts the request from " + receivingEntity;
                case DECLINED -> sendingEntity + " declined the request from " + receivingEntity;
                case CANCELLED -> sendingEntity + " cancelled the request with " + receivingEntity;
                default -> sendingEntity + " sends a request to " + receivingEntity;
            };
        }

        return resultString;
    }

    public void sendFeedbackIMMessage(String feedbackId, String sender, String receiver) {

        log.info("Sending Feedback {} message from: {} to {}", feedbackId, sender, receiver);
        InstantMessage message = setupIMMessageForUser(feedbackService.prepareFeedbackMessage(feedbackId, sender, receiver));
        InstantMessageDtoMapper mapper = new InstantMessageDtoMapper();
        InstantMessageDto dto = mapper.toInstantMessageDto(message);
        dto.setContent(feedbackService.populateFeedbackMessageContent(feedbackId));
        simpleMessagingTemplate.convertAndSendToUser(message.getReceiver(), "/queue/messages",dto);
        log.info("Feedback {} message sent successfully", "");
    }


    @Override
    public void sendFeedbackMessage(String feedbackId, FeedbackMsgDto feedbackMsg) {
        Optional<FeedbackMsg> optFeedbackMsg = feedbackMsgRepository.findByFeedbackId(feedbackId);
        if(optFeedbackMsg.isPresent()) {
            FeedbackMsg defaultFeedback = optFeedbackMsg.get();
            String senderEmail = "";
            String receiverEmail = "";
            if(defaultFeedback.isUserFeedback()) {
                senderEmail = defaultFeedback.getProviderName();
            }
            else {
                Optional<User> optSender = profileService.getProfileOwner(defaultFeedback.getProviderProfileId());
                if(optSender.isPresent()) {
                    senderEmail = optSender.get().getEmail();
                }
            }
            Optional<User> optReceiver = profileService.getProfileOwner(defaultFeedback.getReceiverProfileId());
            if(optReceiver.isPresent()) {
                receiverEmail = optReceiver.get().getEmail();
            }

            if ((!receiverEmail.isEmpty()) && (!senderEmail.isEmpty())) {
                sendFeedbackIMMessage(feedbackId, senderEmail, receiverEmail);
            } else {
                log.warn("Cannot send feedback IM Message: senderEmail {} receiverEmail {}", senderEmail, receiverEmail);
            }
        }
        else {
            log.warn("No Feedback Found - Cannot send feedback IM Message : feedbackId {}", feedbackId);
        }
    }

    private InstantMessage getNewInstantMessage(InstantMessage msg, String receiver) {
        InstantMessage newMessage = new InstantMessage();
        newMessage.setMessageId("IM-" + System.currentTimeMillis());
        newMessage.setSender(msg.getSender());
        newMessage.setReceiver(receiver);
        newMessage.setTimestamp(LocalDateTime.now());
        MessageContent content = new MessageContent();
        content.setContractState(msg.getContent().getContractState());
        content.setContractId(msg.getContent().getContractId());
        content.setMessageType(MessageContent.MessageType.BOOKING_REQUEST);
        content.setContractMessageJson(msg.getContent().getContractMessageJson());
        newMessage.setContent(content);
        newMessage.setSeen(false);
        newMessage.setDismissed(false);
        return newMessage;
    }

    @Transactional
    public void sendVirtualBookingMessagesAfterClaim(String email, String profileId) {
        log.info("Retrieving Virtual Booking Messages for email: {} from profileId: {} messageBox", email, profileId);

        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        Optional<User> optReceiverUser = userRepository.findByEmail(email);
        if (optProfile.isEmpty() || optReceiverUser.isEmpty()) return;

        Profile profile = optProfile.get();
        User receiverUser = optReceiverUser.get();

        for (InstantMessage msg : profile.getMessageBox().getReceivedMessages()) {
            processVirtualMessage(email, profile, receiverUser, msg);
        }

        log.info("Virtual Booking Messages sent successfully to {}", email);
    }

    private void processVirtualMessage(String email, Profile profile, User receiverUser, InstantMessage msg) {
        String json = msg.getContent().getContractMessageJson();
        ContractDetailsDto contractDetails = contractService.convertJsonToObject(json);
        String contractId = contractDetails.getContractId();

        contractRepository.findByContractId(contractId).ifPresent(contract -> {
            contract.setReceivingUser(email);
            contractRepository.save(contract);

            if (contract.getContractState() != ContractState.CANCELLED) {
                enrichContractDetails(contractDetails, contractId, email);
                InstantMessage newMessage = getNewInstantMessage(msg, email);
                receiverUser.getMessageBox().getReceivedMessages().add(newMessage);

                userRepository.save(receiverUser);
                instantMessageRepository.save(newMessage);

                sendVirtualMessageNotification(email, contractDetails, profile.getProfileName(), newMessage);
            }
        });
    }

    private void enrichContractDetails(ContractDetailsDto dto, String contractId, String email) {
        dto.setContractState(ContractState.RECEIVED);
        dto.setPossibleActions(contractService.getPossibleActions(email, contractId, ContractState.RECEIVED));
    }

    private void sendVirtualMessageNotification(String email, ContractDetailsDto contractDetails, String profileName, InstantMessage newMessage) {
        InstantMessageDto dto = new InstantMessageDtoMapper().toInstantMessageDto(newMessage);
        dto.setContent(contractServiceUtil.populateContractMessageContent(contractDetails.getContractId()));
        dto.getContent().getContractContentDto().setActionString(profileName + " has received a booking request");

        log.info("Sending Virtual Booking Message after Claim to email: {}", email);
        simpleMessagingTemplate.convertAndSendToUser(email, "/queue/messages", dto);
        if(contractStateService.receiveContract(contractDetails.getContractId())) {
            log.info("Fake-Receive-Contract message sent for contractId: {}", contractDetails.getContractId());
        }
    }

}
