package com.stageserver.service;

import com.stageserver.dto.calendar.QueryPeriod;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.mapper.SpecialEventDtoMapper;
import com.stageserver.dto.profile.ProfileCalendarDto;
import com.stageserver.model.common.DateRange;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.profile.*;
import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.*;
import com.stageserver.repository.schedule.RecurrenceEndTypeRepository;
import com.stageserver.repository.schedule.RecurrenceRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ScheduleService implements I_ScheduleService {

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private RecurrenceRepository recurrenceRepository;

    @Autowired
    private RecurrenceEndTypeRepository recurrenceEndTypeRepository;

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Override
    @Transactional
    public String addSchedule(String email, String profileId, ScheduleTime scheduleTime) {

        Optional<List<ScheduleTime>> optScheduleList = scheduleTimeRepository.findByProfileId(profileId);
        String scheduleId = utilityService.generateUniqueUUID();
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            if (optScheduleList.isPresent()) {
                List<ScheduleTime> scheduleList = optScheduleList.get();

                scheduleTime.setScheduleId(scheduleId);
                scheduleList.add(scheduleTime);
                profile.setScheduleList(scheduleList);
            } else {
                profile.setScheduleList(List.of(scheduleTime));
            }
            profileRepository.save(profile);
            return scheduleId;
        }
        return null;
    }

    @Override
    public Optional<List<ScheduleTime>> getScheduleList(String email, String profileId) {
        List<ScheduleTime> resultList = new ArrayList<>();
        Optional<List<ScheduleTime>> optScheduleTimeList = scheduleTimeRepository.findForUser(profileId);
        if(optScheduleTimeList.isPresent()) {
            for (ScheduleTime scheduleTime : optScheduleTimeList.get()) {
                Optional<Recurrence> optRecurrence = recurrenceRepository.getRecurrenceByScheduleId(scheduleTime.getScheduleId());
                optRecurrence.ifPresent(scheduleTime::setRecurrence);
                Optional<RecurrenceEndType> optRecurrenceEndType = recurrenceEndTypeRepository.getRecurrenceEndTypeByScheduleId(scheduleTime.getScheduleId());
                optRecurrenceEndType.ifPresent(recurrenceEndType -> scheduleTime.getRecurrence().setRecurrenceEndType(recurrenceEndType));
                resultList.add(scheduleTime);
            }
        }
        return Optional.of(resultList);
    }

    @Override
    @Transactional
    public boolean updateSchedule(String email, String profileId, String scheduleId, ScheduleTime scheduleTime) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForScheduleId(scheduleId);
        if (optScheduleTime.isPresent()) {
            Optional<Recurrence> optRecurrence = recurrenceRepository.getRecurrenceByScheduleId(scheduleId);
            if (optRecurrence.isPresent()) {
                Recurrence recurrence = optRecurrence.get();
                recurrenceRepository.delete(recurrence);
                recurrenceRepository.save(scheduleTime.getRecurrence());
            }

            Optional<RecurrenceEndType> optRecurrenceEndType = recurrenceEndTypeRepository.getRecurrenceEndTypeByScheduleId(scheduleId);
            if (optRecurrenceEndType.isPresent()) {
                recurrenceEndTypeRepository.delete(optRecurrenceEndType.get());
                recurrenceEndTypeRepository.save(scheduleTime.getRecurrence().getRecurrenceEndType());
            }
            ScheduleTime oldScheduleTime = optScheduleTime.get();
            scheduleTime.setElementId(oldScheduleTime.getElementId());
            scheduleTime.setScheduleId(oldScheduleTime.getScheduleId());
            scheduleTimeRepository.save(scheduleTime);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteSchedule(String email, String profileId, String scheduleId) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForScheduleId(scheduleId);
        if(optScheduleTime.isPresent()) {
            ScheduleTime scheduleTime = optScheduleTime.get();
            Optional<Recurrence> optRecurrence = recurrenceRepository.getRecurrenceByScheduleId(scheduleId);
            optRecurrence.ifPresent(recurrence -> recurrenceRepository.delete(recurrence));
            Optional<RecurrenceEndType> optRecurrenceEndType = recurrenceEndTypeRepository.getRecurrenceEndTypeByScheduleId(scheduleId);
            optRecurrenceEndType.ifPresent(recurrenceEndType -> recurrenceEndTypeRepository.delete(recurrenceEndType));
            scheduleTimeRepository.delete(scheduleTime);
            return true;
        }
        return false;
    }

    @Override
    public ScheduleTime getSchedule(String email, String profileId, String scheduleId) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForScheduleId(scheduleId);
        if(optScheduleTime.isPresent()) {
            ScheduleTime scheduleTime = optScheduleTime.get();
            Optional<Recurrence> optRecurrence = recurrenceRepository.getRecurrenceByScheduleId(scheduleId);
            optRecurrence.ifPresent(scheduleTime::setRecurrence);
            Optional<RecurrenceEndType> optRecurrenceEndType = recurrenceEndTypeRepository.getRecurrenceEndTypeByScheduleId(scheduleId);
            optRecurrenceEndType.ifPresent(recurrenceEndType -> scheduleTime.getRecurrence().setRecurrenceEndType(recurrenceEndType));
            return scheduleTime;
        }
        return null;
    }

    public DateRange calculateDateRange(ZonedDateTime startDate, QueryPeriod period) {

        ZonedDateTime endDate = null;

        switch (period) {
            case DAY:
                endDate = startDate.with(LocalTime.MAX);
                break;
            case WEEK:
                startDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
                endDate = startDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY)).with(LocalTime.MAX);
                break;
            case MONTH:
                startDate = startDate.with(TemporalAdjusters.firstDayOfMonth());
                endDate = startDate.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                break;
            default:
                throw new IllegalArgumentException("Invalid period");
        }
        return new DateRange(startDate, endDate);
    }

    @Override
    public Optional<ProfileCalendarDto> getProfileCalendar(String email, String profileId, ZonedDateTime startDate, QueryPeriod period) {
        ProfileCalendarDto profileCalendarDto = new ProfileCalendarDto();
        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
        Optional<List<ScheduleTime>> optScheduleList = getScheduleList(email, profileId);
        SpecialEventDtoMapper specialEventDtoMapper = new SpecialEventDtoMapper();
        if(optScheduleList.isPresent()) {
            profileCalendarDto.setScheduleTimeDtoList(scheduleTimeDtoMapper.toScheduleTimeDtoList(optScheduleList.get()));
        }

        DateRange dateRange = calculateDateRange(startDate, period);
        Optional<List<SpecialEvent>> optSpecialEvent = specialEventRepository.findSpecialEventsByProfileIdAndDateRange(profileId, dateRange.getStartDate(), dateRange.getEndDate());
        if(optSpecialEvent.isPresent()) {
            List<SpecialEvent> specialEventList = optSpecialEvent.get();
            for(SpecialEvent specialEvent : specialEventList) {
                Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForSpecialEventId(specialEvent.getSpecialEventId());
                if(optScheduleTime.isPresent()) {
                    specialEvent.setScheduleTime(optScheduleTime.get());
                }
            }
            profileCalendarDto.setSpecialEventDtoList(specialEventDtoMapper.toSpecialEventDtoList(optSpecialEvent.get()));
        }
        return Optional.of(profileCalendarDto);
    }

}
