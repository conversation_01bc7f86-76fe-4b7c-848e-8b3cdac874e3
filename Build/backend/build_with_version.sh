#!/bin/bash

# --- Get latest Git tag in proper version order ---
echo "Getting latest Git tag..."
LATEST_TAG=$(git tag | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | sort -V | tail -n 1)

# Default fallback version if no tag is found
DEFAULT_VERSION="0.0.1-SNAPSHOT"

# Use the latest tag if available, strip the leading 'v'
if [ -n "$LATEST_TAG" ]; then
  VERSION="${LATEST_TAG#v}"
else
  echo "No Git tag found. Using default version: $DEFAULT_VERSION"
  VERSION="$DEFAULT_VERSION"
fi

echo "Building with version: $VERSION (from git tag: ${LATEST_TAG:-none})"

# Require xmlstarlet
if ! command -v xmlstarlet &> /dev/null; then
  echo " xmlstarlet is required. Install it with: sudo apt install xmlstarlet"
  exit 1
fi

# Update ONLY the <project><version> tag (not the parent version)
xmlstarlet ed -L -u "/_:project/_:version" -v "$VERSION" pom.xml

# Build the jar
mvn clean package

# Output the version from the built JAR
JAR_FILE=$(ls target/*.jar | head -n 1)
if [ -f "$JAR_FILE" ]; then
  echo "JAR built: $JAR_FILE"
  unzip -p "$JAR_FILE" META-INF/MANIFEST.MF | grep Implementation-Version
else
  echo "Build failed. No JAR found."
  exit 1
fi


git checkout pom.xml
