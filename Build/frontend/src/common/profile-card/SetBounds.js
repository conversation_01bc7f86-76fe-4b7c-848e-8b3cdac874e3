import { useEffect } from "react";
import { useMapInstance } from "./MapProvider";

const SetBounds = ({ markers }) => {
  const map = useMapInstance();

  useEffect(() => {
    if (markers?.length === 1 && markers[0].lat && markers[0].lng) {
      map.setView([markers[0].lat, markers[0].lng], 13);
    } else if (markers?.length > 1 && markers[0].lat && markers[0].lng) {
      const bounds = markers.map((marker) => [marker.lat, marker.lng]);
      map.fitBounds(bounds);
    }
  }, [markers, map]);

  return null;
};

export default SetBounds;
