import { Box, Typography } from "@mui/material";
import React from "react";
//import Rating from "@/component/rating/rating.components";
import { Button } from "@/component";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import CardCarousel from "./profile-card.carousel.common";
import { generateLocationString } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
//import FilledHeart from "@/assets/svg/FilledHeart.svg";
//import LikeIcon from "@/assets/svg/LikeIcon.svg";
import { useSelector } from "react-redux";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import { useRouter } from "next/navigation";
import classNames from "classnames";
import { SouthEast } from "@mui/icons-material";
// import {
//   addFavouriteByProfileID,
//   removeFavouriteByProfileID,
// } from "@/store/slice/common/favourite.slice";

const ProfileSingleCard = ({ profilesData }) => {
  // const t = useTranslations("profiles");
  const a = useTranslations("act");
  const lang = useLocale();
  const { token } = useSelector((state) => state.login);
  const { setIsTokenExpired } = useAppContext();
  const router = useRouter();
  //const dispatch = useDispatch();

  if (!profilesData) return null; // Ensures no errors if data is undefined

  const clickHandler = (profileId) => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }
    router.push(`/${lang}/${profileId}/view`);
  };

  // // Handle Add to Favorite
  // const handleAddFavourite = () => {
  //   if (!token) {
  //     setIsTokenExpired(true);
  //     return;
  //   }
  //   dispatch(addFavouriteByProfileID({ profileId: profilesData.profileId }));
  // };

  // // Handle Remove from Favorite
  // const handleRemoveFavourite = () => {
  //   dispatch(removeFavouriteByProfileID({ profileId: profilesData.profileId }));
  // };

  return (
    <Box className="!flex !flex-wrap !gap-5 !mt-4 !pb-28 lg:!pb-0 !px-4 lg:!px-0">
      <Box
        key={profilesData.profileId}
        className={classNames(
          "border mb-4 !w-full md:!max-w-xs lg:!w-[330px] rounded-[4px] bg-[--footer-bg] cursor-pointer border-[--divider-color]",
        )}
      >
        {/* Profile Image Carousel */}
        <Box className="!relative">
          <CardCarousel
            images={profilesData.profileImageUrls}
            className="slider-class"
            profileId={profilesData.profileId}
          />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
            {profilesData.profileType}
          </Typography>
        </Box>

        <Box className="!px-5">
          {/* Profile Name and Favourite Button */}
          <Box className="!flex !justify-between !items-center pt-2">
            <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
              {profilesData.profileName.length > 18
                ? profilesData.profileName.substring(0, 18) + "..."
                : profilesData.profileName}
            </Typography>
            {/* <IconButton onClick={profilesData.favouriteSelected ? handleRemoveFavourite : handleAddFavourite}>
              {profilesData.favouriteSelected ? <FilledHeart className="!text-2xl" /> : <LikeIcon className="!text-2xl" />}
            </IconButton> */}
          </Box>

          {/* Followers & Pricing */}
          <Box className="lg:!flex !gap-x-3 !pb-5 pt-4">
            <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
              <Typography className="!text-left !text-[--text-color] font-craftWorkRegular">
                {profilesData?.numberOfFollowers} <br /> {a("followers")}
              </Typography>
            </Box>
            {(profilesData.profileType === "ACT_PROFILE" ||
              profilesData.profileType === "VIRTUAL_ACT_PROFILE") && (
              <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
                <Typography className="!text-left !text-sm !text-[--text-color] !font-craftWorkRegular">
                  {a("average")} ${profilesData?.averagePayForGig}k/gig*
                </Typography>
              </Box>
            )}
            {(profilesData.profileType === "VENUE_PROFILE" ||
              profilesData.profileType === "VIRTUAL_VENUE_PROFILE") && (
              <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
                <Typography className="!text-left !text-sm !text-[--text-color] !font-craftWorkRegular">
                  {a("average")} ${profilesData?.averagePricePerBooking} /bookings*
                </Typography>
              </Box>
            )}
          </Box>

          {/* Location */}
          <Box className="!flex !items-center !gap-x-2 pb-4">
            <span className="!text-2xl">
              <LocationSvg />
            </span>
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {generateLocationString(profilesData.locationDto).length > 32
                ? generateLocationString(profilesData.locationDto).substring(0, 32) + "..."
                : generateLocationString(profilesData.locationDto)}
            </Typography>
          </Box>

          {/* View Profile Button */}
          <Box className="!flex !gap-1 !mb-4">
            <Button
              className="!bg-[--text-color] !w-full px-3 py-3 !gap-x-2"
              sx={{
                minWidth: 0,
                padding: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={() => {
                clickHandler(profilesData.profileId);
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                View Profile
              </Typography>
              <SouthEast className="size-5 text-[--bg-color]" />
              {/* <Ticket className="size-4" /> */}
            </Button>
            {/* </Link> */}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ProfileSingleCard;
