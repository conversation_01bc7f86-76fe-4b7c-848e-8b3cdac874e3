import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { generateLocationString } from "@/utils";
import "@/styles/info-window.css";
import { GoogleMap, MarkerF, InfoWindowF, useLoadScript } from "@react-google-maps/api";

const MapSearch = ({ profiles }) => {
  const [mapData, setMapData] = useState([]);
  const [selectedMarker, setSelectedMarker] = useState(null);
  // console.log('Google Maps API Key:', process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
    libraries: ["places"],
  });
  useEffect(() => {
    const mapData = profiles?.content
      ?.filter((profile) => {
        if (profile?.eventId) {
          return profile?.venueLocation?.latitude && profile?.venueLocation?.longitude;
        }
        return profile?.locationDto?.latitude && profile?.locationDto?.longitude;
      })
      ?.map((profile) => ({
        id: profile.eventId || profile.profileId,
        lat: profile?.eventId
          ? parseFloat(profile?.venueLocation?.latitude)
          : parseFloat(profile?.locationDto?.latitude),
        lng: profile?.eventId
          ? parseFloat(profile?.venueLocation?.longitude)
          : parseFloat(profile?.locationDto?.longitude),
        properties: {
          name: profile.eventName || profile.profileName,
          Address: profile?.eventId
            ? generateLocationString(profile?.venueLocation)
            : generateLocationString(profile?.locationDto),
          url:
            profile?.eventMediaInfo?.imageUrls?.[0] ||
            profile?.profileImageUrls?.[0] ||
            profile?.mediaDto?.imageUrls?.[0],
        },
      }));

    setMapData(mapData);
  }, [profiles]);

  if (!isLoaded) return <div>Loading...</div>;

  const mapCenter =
    mapData.length > 0
      ? { lat: mapData[0].lat, lng: mapData[0].lng }
      : { lat: 20.5937, lng: 78.9629 }; // Default to India's center

  return (
    <Box className="flex bg-slate-100 lg:w-[71.5vw] w-[99vw] lg:h-full h-fit rounded-[8px] border border-[--divider-color]">
      <GoogleMap
        zoom={7}
        center={mapCenter}
        mapContainerClassName="w-[100vw] lg:h-full h-[59vh]"
        options={{
          fullscreenControl: false,
          streetViewControl: false,
        }}
      >
        {mapData.map((marker) => (
          <MarkerF
            key={marker.id}
            position={{ lat: marker.lat, lng: marker.lng }}
            onClick={() => setSelectedMarker(marker)}
          />
        ))}

        {selectedMarker && (
          <InfoWindowF
            position={{ lat: selectedMarker.lat, lng: selectedMarker.lng }}
            onCloseClick={() => setSelectedMarker(null)}
          >
            <div className="info-window-content">
              {selectedMarker.properties.url && (
                <img src={selectedMarker.properties.url} alt={selectedMarker.properties.name} />
              )}
              <h3>{selectedMarker.properties.name}</h3>
              <p>{selectedMarker.properties.Address}</p>
            </div>
          </InfoWindowF>
        )}
      </GoogleMap>
    </Box>
  );
};

export default MapSearch;
