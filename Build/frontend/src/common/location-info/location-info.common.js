import { Box, Typography } from "@mui/material";
import React from "react";
import FacebookSvg from "@/assets/svg/FacebookSvg.svg";
import InstagramSvg from "@/assets/svg/Instagram.svg";
import YoutubeSvg from "@/assets/svg/Youtube.svg";
import LocationIconSvg from "@/assets/svg/LocationIconSvg.svg";
import { useSelector } from "react-redux";
import Link from "next/link";
import { generateLocationString } from "@/utils";
import { useTranslations } from "next-intl";

const LocationInfo = ({ className }) => {
  const t = useTranslations("actPreview");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className={`${className} !border !border-[--divider-color]  !rounded-[4px] !p-5`}>
      <Box className="!flex !gap-x-3">
        <LocationIconSvg className="!w-6 !h-6" />
        <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular !pb-3">
          {generateLocationString(previewData?.actLocation).length > 32
            ? generateLocationString(previewData?.actLocation).substring(0, 32) + "..."
            : generateLocationString(previewData?.actLocation)}
        </Typography>
      </Box>
      {/* <Box className="!flex !gap-x-3">
        <GlobeIcon className="!w-6 !h-6" />
        <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular !pb-3">
          {t("locationInfo.travelDistance")}
        </Typography>
      </Box>
      <Box className="!flex !gap-x-3">
        <UserGroup className="!w-6 !h-6" />
        <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular">
          {t("locationInfo.seniorEntertainment")}
        </Typography>
      </Box> */}
      <Box className="!flex !gap-x-4 !py-4">
        {/** social links */}
        <Link href={previewData ? `https:/facebook/${previewData.facebookLink}` : ""}>
          <FacebookSvg className="!w-6 !h-6" />
        </Link>
        <Link href={previewData ? `https:/instagram/${previewData.instagramLink}` : ""}>
          <InstagramSvg className="!w-6 !h-6" />
        </Link>
        <Link href={previewData ? `https:/youtube/${previewData.youtubeLink}` : ""}>
          <YoutubeSvg className="!w-6 !h-6" />
        </Link>
      </Box>
      <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular">
        {t("locationInfo.standard")}
      </Typography>
      <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskSemiBold !pb-3">
        {`$ ${previewData?.actPayment?.typicalPrice ?? 0} / per ${
          previewData?.actPayment?.standardPricePer.toLowerCase() ?? "event"
        }`}
      </Typography>
      <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular">
        {t("locationInfo.charity")}
      </Typography>
      <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskSemiBold !pb-3">
        {`$ ${previewData?.actPayment?.charityPrice ?? 0} / per ${
          previewData?.actPayment?.minimalPricePer.toLowerCase() ?? "event"
        }`}
      </Typography>
    </Box>
  );
};

export default LocationInfo;
