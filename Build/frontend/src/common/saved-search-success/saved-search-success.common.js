"use client";
import {
  Box,
  Dialog,
  DialogContent,
  Drawer,
  Icon<PERSON><PERSON>on,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React from "react";
import SavedSearchSuccessSvg from "@/assets/svg/VirtualAct.svg";
import { Clear } from "@mui/icons-material";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/component";

const SavedSearchSuccess = ({ open, handleClose }) => {
  const t = useTranslations("savedSearchSuccess");
  const s = useTranslations("leaveFeedback");
  const lang = useLocale();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const content = (
    <DialogContent className="!max-w-xl !bg-[--footer-bg] lg:!border-[1px] md:!border-[1px] !px-4 flex lg:flex-row md:flex-row flex-col gap-3 lg:!pt-4 md:pt-4 justify-center lg:items-start items-center md:!border-[--text-color] lg:!border-[--text-color]">
      <Box>
        <SavedSearchSuccessSvg className="text-5xl" />
        <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color] my-3">
          {t("savedSuccessfully")}
        </Typography>
        <Typography className="text-sm CraftworkGroteskRegular pb-4 text-[--text-color]">
          {t("fineThisSaved")}
          <Link href={`/${lang}/favourites/saved-searches`} className="underline">
            {t("favoritespage")}
          </Link>
          .
        </Typography>
      </Box>
      <IconButton
        onClick={handleClose}
        sx={{
          "&.MuiButtonBase-root": {
            alignItems: "start",
          },
          "&.MuiButtonBase-root:hover": {
            backgroundColor: "transparent !important",
          },
        }}
        className="hidden md:flex lg:flex"
      >
        <Clear className="text-lg text-[--text-color]" />
      </IconButton>
      <Box
        className={` ${isMobile ? "fixed flex justify-between items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "gap-x-4"}`}
      >
        <Button
          className=" w-5/12 h-[39px] flex lg:hidden !gap-x-2 items-center"
          onClick={handleClose}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--text-color]">
            {s("cancel")}
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>
      </Box>
    </DialogContent>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default SavedSearchSuccess;
