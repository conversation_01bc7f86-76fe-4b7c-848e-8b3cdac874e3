"use client";
import { <PERSON><PERSON>, CheckBox } from "@/component";
import { Box, Typography } from "@mui/material";
import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import ClockIcon from "@/assets/svg/ClockIcon.svg";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Copy from "@/assets/svg/Copy.svg";

import { setHours, setMinutes } from "date-fns";
import { useTranslations } from "next-intl";

const VenueWorkingHour = ({ control, setValue, currentProfile, watch }) => {
  const t = useTranslations("venueWorkingHour");
  const days = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];
  //const [selectedDays, setSelectedDays] = useState(["Mo"]);
  const startTime = setHours(setMinutes(new Date(), 0), 9);
  const endTime = setHours(setMinutes(new Date(), 0), 18);
  const enableworkinghour = watch("enableworkinghour");
  const updatedWorkingHoursList = currentProfile?.infoDto?.weeklyWorkingHours?.workingHoursList;
  //console.log(updatedWorkingHoursList, "updatedWorkingHoursList");
  // const { fields, append, remove } = useFieldArray({
  //   control,
  //   name: "workingHoursList"
  // });
  const [workingHoursList, setWorkingHoursList] = useState([
    {
      day: "MONDAY",
      startTime: setHours(setMinutes(new Date(), 0), 9),
      endTime: setHours(setMinutes(new Date(), 0), 18),
    },
  ]);

  useEffect(() => {
    if (updatedWorkingHoursList && updatedWorkingHoursList.length > 0) {
      const daysOrder = {
        MONDAY: 1,
        TUESDAY: 2,
        WEDNESDAY: 3,
        THURSDAY: 4,
        FRIDAY: 5,
        SATURDAY: 6,
        SUNDAY: 7,
      };
      //const days = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];
      const update = updatedWorkingHoursList.map((wh) => {
        return {
          ...wh,
          startTime: new Date(wh.startTime),
          endTime: new Date(wh.endTime),
        };
      });

      const sortedWorkingHours =
        update &&
        update?.sort((a, b) => {
          // update set days as  well
          // setSelectedDays((prev) =>
          //   prev.includes(days[daysOrder[a.day] - 1])
          //     ? prev.filter((d) => d !== days[daysOrder[a.day] - 1])
          //     : [...prev, days[daysOrder[a.day] - 1]],
          // );

          return daysOrder[a.day] - daysOrder[b.day];
        });

      setWorkingHoursList(sortedWorkingHours);
    }

    //setWorkingHoursList(updatedWorkingHoursList)
  }, [updatedWorkingHoursList]);

  // updatedWorkingHoursList && setWorkingHoursList(updatedWorkingHoursList);

  // useEffect(() => {
  //   setValue("setEnableworkinghour", enableworkinghour);
  // }, []);

  const toggleDaySelection = (day) => {
    const daysOrder = {
      MONDAY: 1,
      TUESDAY: 2,
      WEDNESDAY: 3,
      THURSDAY: 4,
      FRIDAY: 5,
      SATURDAY: 6,
      SUNDAY: 7,
    };
    const daysMap = {
      Mo: "MONDAY",
      Tu: "TUESDAY",
      We: "WEDNESDAY",
      Th: "THURSDAY",
      Fr: "FRIDAY",
      Sa: "SATURDAY",
      Su: "SUNDAY",
    };

    const selectedDay = daysMap[day];
    const newWorkingHour = [{ day: selectedDay, startTime, endTime }];
    // do not append if already exist
    //if (workingHoursList?.some((wh) => wh.day === selectedDay)) return;

    //if already exist remove it otherwise append it
    const index = workingHoursList?.findIndex((wh) => wh.day === selectedDay);
    if (index > -1) {
      const newWorkingHours = [...workingHoursList];
      //remove(index);
      newWorkingHours.splice(index, 1);
      //newWorkingHours should be in order  as daysMap
      const sortedWorkingHours =
        newWorkingHours &&
        newWorkingHours?.sort((a, b) => {
          return daysOrder[a.day] - daysOrder[b.day];
        });
      setWorkingHoursList(sortedWorkingHours);
      //setValue("workingHoursList", sortedWorkingHours);
      //setSelectedDays((prev) => prev.filter((d) => d !== day));
      return;
    }
    const newWorkingHours = [...workingHoursList, ...newWorkingHour];
    const sortedWorkingHours = newWorkingHours?.sort((a, b) => {
      return daysOrder[a.day] - daysOrder[b.day];
    });
    // console.log(sortedWorkingHours, "sortedWorkingHours");
    setWorkingHoursList(sortedWorkingHours);
    // setSelectedDays((prev) =>
    //   prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day],
    // );
  };

  useEffect(() => {
    setValue("workingHoursList", workingHoursList);
  }, [workingHoursList]);

  const checkDays = (day) => {
    // check if day present in workingHoursList
    const daysMap = {
      Mo: "MONDAY",
      Tu: "TUESDAY",
      We: "WEDNESDAY",
      Th: "THURSDAY",
      Fr: "FRIDAY",
      Sa: "SATURDAY",
      Su: "SUNDAY",
    };
    const selectedDay = daysMap[day];
    // check if day present in workingHoursList and return true or false
    return workingHoursList?.some((wh) => wh.day === selectedDay);
  };

  // useEffect(() => {
  // setValue("enableworkinghour", enableworkinghour);
  // }, [enableworkinghour]);
  return (
    <>
      <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium pt-10 ">
        {t("workingHour")}
      </Typography>
      <Box className="w-full">
        {/* <Box className="flex items-center !mb-2">
          <CheckBox
            className="!max-w-[24px]"
            sx={{ color: "#EFEFEF", marginRight: "5px" }}
            checked={enableworkinghour}
            onChange={(e) => {
              setEnableworkinghour(!enableworkinghour);
            }}
          />
          <label
            htmlFor={"enableworkinghour"}
            className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
          >
            Enable working hours
          </label>
        </Box> */}

        <Controller
          name="enableworkinghour"
          control={control}
          render={({ field }) => (
            <Box className="flex items-center !mb-2">
              <CheckBox
                className="!max-w-[24px]"
                sx={{ color: "#EFEFEF", marginRight: "5px" }}
                checked={field.value}
                onChange={(e) => field.onChange(e.target.checked)}
              />
              <label
                htmlFor={"enableworkinghour"}
                className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
              >
                {t("enableWorkingHour")}
              </label>
            </Box>
          )}
        />

        {enableworkinghour && (
          <Box>
            <Box className="flex flex-wrap items-center gap-2">
              {days.map((day, index) => (
                <Box
                  key={index}
                  className={classNames(
                    "w-[37px] h-[37px] flex items-center justify-center border rounded-full",
                    {
                      "border-[--inprogress-color]": checkDays(day),
                      "border-[--divider-color]": !checkDays(day),
                    },
                  )}
                  onClick={() => toggleDaySelection(day)}
                  style={{ cursor: "pointer" }}
                >
                  <Typography
                    className={classNames("text-sm CraftworkGroteskRegular", {
                      "text-[--inprogress-color]": checkDays(day),
                      "text-[--text-color]": !checkDays(day),
                    })}
                  >
                    {day}
                  </Typography>
                </Box>
              ))}
            </Box>
            {workingHoursList.map((field, index) => (
              <Box
                className="lg:grid grid-cols-12 items-center gap-2 pt-4 lg:pt-0 pb-4"
                key={field.day}
              >
                <Typography className="col-span-2 text-sm text-[--text-color] CraftworkGroteskGX">
                  {field.day}
                </Typography>
                <Box className="flex flex-wrap col-span-10 gap-2 items-center w-full pt-4">
                  <Box className="">
                    <Typography className="text-sm text-[--text-color] font-craftWorkGX">
                      {t("startTime")}
                    </Typography>
                    <Box className="border border-[--text-color] h-[58px]">
                      <ReactDatePicker
                        selected={field.startTime}
                        onChange={(time) => {
                          const updatedWorkingHoursList = [...workingHoursList];
                          updatedWorkingHoursList[index].startTime = time;
                          setWorkingHoursList(updatedWorkingHoursList);
                        }}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        showIcon
                        icon={<ClockIcon className="text-2xl lg:mt-2 mt-2" />}
                        className=" border ml-8 react-date-picker w-[150px]"
                        name={`workingHoursList.${index}.startTime`}
                      />
                    </Box>
                  </Box>
                  <Box className="">
                    <Typography className="text-sm text-[--text-color] font-craftWorkGX">
                      {t("endTime")}
                    </Typography>
                    <Box className="border border-[--text-color] h-[58px]">
                      <ReactDatePicker
                        selected={field.endTime}
                        onChange={(time) => {
                          const updatedWorkingHoursList = [...workingHoursList];
                          updatedWorkingHoursList[index].endTime = time;
                          setWorkingHoursList(updatedWorkingHoursList);
                        }}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        showIcon
                        icon={<ClockIcon className="text-2xl lg:mt-2 mt-2" />}
                        className=" border ml-8 react-date-picker w-[150px]"
                        name={`workingHoursList.${index}.endTime`}
                      />
                    </Box>
                  </Box>
                  {field.day === "MONDAY" && (
                    <Button
                      className="normal-case flex items-center gap-1"
                      sx={{
                        border: 0,
                        padding: 0,
                        "&.MuiButtonBase-root": {
                          color: "transparent !important",
                        },
                      }}
                      onClick={() => {
                        const updatedWorkingHoursList = [...workingHoursList];
                        updatedWorkingHoursList.map((wh) => {
                          wh.startTime = field.startTime;
                          wh.endTime = field.endTime;
                        });
                        setWorkingHoursList(updatedWorkingHoursList);
                      }}
                    >
                      <Copy className="h-6 w-6" />
                      <Typography className="text-sm text-[--text-color] font-craftWorkHeavy underline">
                        {t("copyToAll")}
                      </Typography>
                    </Button>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Box>
    </>
  );
};

export default VenueWorkingHour;
