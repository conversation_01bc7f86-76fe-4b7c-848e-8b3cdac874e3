import { Box, Typography } from "@mui/material";
import React from "react";
import { Button, CommonImage } from "@/component";
import { SouthEast } from "@mui/icons-material";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import SentTag from "@/common/(tags)/tags-sent/tag-sent.common";
import ConfirmedTag from "@/common/(tags)/tags-confirmed/tags-confirmed.common";
import DeclinedTag from "@/common/(tags)/tags-declined/tags-declined.common";
import NegotiatingTag from "@/common/(tags)/tags-negotiating/tags-negotiating.common";
import Paginate from "@/containers/paginate/paginate.container";
import classNames from "classnames";

const ContractsCard = ({ contracts }) => {
  const contractData = contracts?.content;
  const router = useRouter();
  const lang = useLocale();

  const handleCardClick = (contractId) => {
    if (contractId) {
      router.push(`/${lang}/booking-details?contract-id=${contractId}`);
    }
  };

  const handleButtonClick = (e, path) => {
    e.stopPropagation();
    router.push(path);
  };

  return (
    <Box className="lg:!pt-6">
      {contractData?.length > 0 &&
        contractData?.map((data) => {
          return (
            <Box
              key={data.id}
              className="!h-fit p-4 !mb-4 !cursor-pointer hover:!bg-[--divider-color] !border !rounded-[4px] !border-[--divider-color] !bg-[--footer-bg]"
              onClick={() => handleCardClick(data?.contractId)}
            >
              {/* Main card layout as flex with better responsive behavior */}
              <Box className="flex flex-col lg:flex-row w-full">
                {/* First row: Profile info and action buttons side by side */}
                <Box className="flex flex-col sm:flex-row justify-between w-full lg:w-8/12 mb-4 lg:mb-0">
                  {/* Profile info section */}
                  <Box className="flex flex-wrap items-center gap-2 mb-3 sm:mb-0">
                    {data.bookingParty === "VENUE" && data?.venueProfileImageUrls?.[0] && (
                      <CommonImage
                        src={data?.venueProfileImageUrls?.[0]}
                        alt="image"
                        width={60}
                        height={60}
                        className="rounded-full w-10 h-10 object-cover flex-shrink-0"
                      />
                    )}
                    {data.bookingParty === "ACT" && data?.actProfileImageUrls?.[0] && (
                      <CommonImage
                        src={data?.actProfileImageUrls?.[0]}
                        alt="image"
                        width={60}
                        height={60}
                        className="rounded-full w-10 h-10 object-cover flex-shrink-0"
                      />
                    )}

                    {data.bookingParty === "VENUE" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data.venueProfileName}]`}
                      </Typography>
                    )}
                    {data.bookingParty === "USER" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data?.purchaserInfo?.firstName} ${data?.purchaserInfo?.lastName}]`}
                      </Typography>
                    )}
                    {data.bookingParty === "ACT" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data.actProfileName}]`}
                      </Typography>
                    )}

                    {data.otherParty === "VENUE" && data?.venueProfileImageUrls?.[0] && (
                      <CommonImage
                        src={data?.venueProfileImageUrls?.[0]}
                        alt="image"
                        width={60}
                        height={60}
                        className="rounded-full w-10 h-10 object-cover flex-shrink-0"
                      />
                    )}
                    {data.otherParty === "ACT" && data?.actProfileImageUrls?.[0] && (
                      <CommonImage
                        src={data?.actProfileImageUrls?.[0]}
                        alt="image"
                        width={60}
                        height={60}
                        className="rounded-full w-10 h-10 object-cover flex-shrink-0"
                      />
                    )}

                    {data.otherParty === "VENUE" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data.venueProfileName}]`}
                      </Typography>
                    )}
                    {data.otherParty === "USER" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data?.purchaserInfo?.firstName} ${data?.purchaserInfo?.lastName}]`}
                      </Typography>
                    )}
                    {data.otherParty === "ACT" && (
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                        {`[ ${data.actProfileName}]`}
                      </Typography>
                    )}

                    {data?.contractTag && <Box>{data?.contractTag}</Box>}
                  </Box>

                  {/* Action buttons with fixed position */}
                  <Box className="flex items-center sm:justify-end">
                    <Box className="flex flex-wrap gap-2">
                      {data?.eventId && data?.editable && (
                        <Button
                          onClick={(e) =>
                            handleButtonClick(e, `/${lang}/event/${data?.eventId}/main-info`)
                          }
                          className="normal-case !bg-[--text-color] flex gap-2"
                        >
                          <Typography className="text-sm CraftworkGroteskHeavy text-[--bg-color]">
                            View Event
                          </Typography>
                          <SouthEast className="size-5 text-[--bg-color]" />
                        </Button>
                      )}
                      {data?.displayFeedback && (
                        <Button
                          onClick={(e) =>
                            handleButtonClick(e, `/${lang}/${data?.feedbackId}/leave-a-feedback`)
                          }
                          className="normal-case !bg-[--text-color] flex gap-2"
                        >
                          <Typography className="text-sm CraftworkGroteskHeavy text-[--bg-color]">
                            Provide Feedback
                          </Typography>
                          <SouthEast className="size-5 text-[--bg-color]" />
                        </Button>
                      )}
                      {data?.updateFeedback && (
                        <Button
                          onClick={(e) =>
                            handleButtonClick(e, `/${lang}/${data?.feedbackId}/leave-a-feedback`)
                          }
                          className="normal-case !bg-[--text-color] flex gap-2"
                        >
                          <Typography className="text-sm CraftworkGroteskHeavy text-[--bg-color]">
                            Update Feedback
                          </Typography>
                          <SouthEast className="size-5 text-[--bg-color]" />
                        </Button>
                      )}
                      {data?.paymentStatus !== "NO_PAYMENT" && (
                        <Box
                          className={classNames(
                            "!flex !gap-x-1 !items-center !p-1 !rounded-[2px]",
                            {
                              "bg-[--inprogress-color]":
                                data?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" ||
                                data?.paymentStatus?.toUpperCase() ===
                                  "WAITING_OTHER_PARTY_PAYMENT",
                              "bg-[--confirmed-color]":
                                data?.paymentStatus?.toUpperCase() === "FINALIZED",
                              "bg-[--text-color] ": ![
                                "PAYMENT_REQUIRED",
                                "WAITING_OTHER_PARTY_PAYMENT",
                                "FINALISED",
                              ]?.includes(data?.paymentStatus?.toUpperCase()),
                            },
                          )}
                        >
                          <Typography
                            className={classNames("!text-sm CraftworkGroteskHeavy", {
                              "!text-white": [
                                "PAYMENT_REQUIRED",
                                "WAITING_OTHER_PARTY_PAYMENT",
                              ].includes(data?.paymentStatus?.toUpperCase()),
                              "!text-[--bg-color]": ![
                                "PAYMENT_REQUIRED",
                                "WAITING_OTHER_PARTY_PAYMENT",
                              ].includes(data?.paymentStatus?.toUpperCase()),
                            })}
                          >
                            {data?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" &&
                              "Payment Required"}
                            {data?.paymentStatus?.toUpperCase() === "WAITING_OTHER_PARTY_PAYMENT" &&
                              "Waiting Other Party Payment"}
                            {data?.paymentStatus?.toUpperCase() === "FINALIZED" && "Paid"}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>

                {/* Second row: Calendar and tags with consistent justification */}
                <Box className="flex flex-wrap items-center justify-between lg:justify-end gap-3 w-full lg:w-4/12">
                  {/* Date information */}
                  <Box className="flex items-center gap-2">
                    <CalenderIcon className="!text-2xl flex-shrink-0" />
                    <Typography className="!text-[--text-color] w-24 !text-sm CraftworkGroteskRegular">
                      {dayjs(data?.scheduleTime?.startDate).format("MMM DD, YYYY")}
                    </Typography>
                  </Box>

                  {/* Price */}
                  {data?.preformancePrice && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {data?.preformancePrice}
                    </Typography>
                  )}

                  {/* Tags */}
                  {data?.tags && <Box>{data?.tags}</Box>}

                  {/* Contract state tags */}
                  {(data?.contractState === "CONFIRMED" || data?.contractState === "COMPLETED") && (
                    <Box>
                      <ConfirmedTag label={data?.contractState} />
                    </Box>
                  )}
                  {data?.contractState === "DECLINED" && (
                    <Box>
                      <DeclinedTag name={"DECLINED"} />
                    </Box>
                  )}
                  {data?.contractState === "CANCELLED" && (
                    <Box>
                      <DeclinedTag name={"CANCELLED"} />
                    </Box>
                  )}
                  {data?.contractState === "SENT" && (
                    <Box>
                      <SentTag name={"SENT"} />
                    </Box>
                  )}
                  {data?.contractState === "NEGOTIATING" && (
                    <Box>
                      <NegotiatingTag />
                    </Box>
                  )}
                  {data?.contractState === "CREATED" && (
                    <Box>
                      <SentTag name={"NEW"} />
                    </Box>
                  )}
                  {data?.contractState === "RECEIVED" && (
                    <Box>
                      <SentTag name={"RECEIVED"} />
                    </Box>
                  )}

                  {/* Contract work link - ALWAYS SHOW THIS SECTION with the SouthEast icon */}
                  <Box className="!flex !gap-x-1 !items-center">
                    <Typography className="!text-[--text-color] !text-sm !underline CraftworkGroteskHeavy">
                      {data?.contractWork}
                    </Typography>
                    <SouthEast
                      className="!text-[--text-color] cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent event bubbling
                        router.push(`/${lang}/booking-details?contract-id=${data?.contractId}`);
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            </Box>
          );
        })}
      <Box className="flex justify-center lg:mb-0 mb-20">
        {contracts?.totalPages > 1 && (
          <Paginate totalRecords={contracts?.totalElements} perPageRecord={contracts.size} />
        )}
      </Box>
    </Box>
  );
};

export default ContractsCard;
