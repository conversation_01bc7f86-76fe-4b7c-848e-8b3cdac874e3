"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Button, CommonImage } from "@/component";
import { East } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import React from "react";
import SuccessBookingSvg from "@/assets/svg/SuccessBookingSvg.svg";
import FeedbackSuccess from "@/assets/png/FeedbackSuccess.png";
import Link from "next/link";
import { useLocale } from "next-intl";

const BookingSuccess = () => {
  const lang = useLocale();
  return (
    <Box className="lg:!flex !flex-row !w-full !min-h-screen">
      <Box className="lg:basis-1/2 w-full ">
        <ProfileNavbar
          tag="Booking request"
          className=" h-[64px] !fixed !top-0 lg:max-w-[49%] !right-0 !left-0  !bg-[--bg-color] !z-20"
        />
        <Box className="max-w-3xl lg:pt-60 pt-5 lg:px-10 px-5">
          <CommonImage
            src={FeedbackSuccess}
            alt="feedback-success"
            className="lg:w-1/2 w-full lg:hidden rounded-[1px] mb-10 object-center lg:h-full h-[240px]"
          />
          <SuccessBookingSvg className="w-12 h-12" />
          <Typography className="text-[--text-color] text-2xl font-craftWorkMedium pt-5">
            Success!
            <br />
            Booking request has been sent
          </Typography>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular pt-10">
            Act will review it and let you know. Your copy has beed saved to the{" "}
            <Link href={`/${lang}/contracts/contracts-by-me`} className="underline">
              Contracts page
            </Link>
            .
          </Typography>
        </Box>
        <Box className="border-t border-t-[--divider-color] fixed right-0 max-w-[49%] left-0 z-20 bottom-0 bg-[--bg-color] px-8 h-[70px] items-center flex justify-end">
          <Link href={`/${lang}/`}>
            <Button className="!bg-[--text-color] flex gap-1 !normal-case">
              <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">
                Done
              </Typography>
              <East className="text-[--bg-color]" />
            </Button>
          </Link>
        </Box>
      </Box>
      <Box className="lg:!block hidden lg:basis-1/2">
        <CommonImage
          src={FeedbackSuccess}
          alt="feedback-success"
          className="lg:w-1/2 w-full px-5 lg:px-0 object-center h-full lg:fixed"
        />
      </Box>
    </Box>
  );
};

export default BookingSuccess;
