"use client";
import {
  Box,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useState } from "react";
import VenueProfileSvg from "@/assets/svg/VenueProfile.svg";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import Link from "next/link";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { East, RadioButtonUnchecked } from "@mui/icons-material";
import classNames from "classnames";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import HomeSvg from "@/assets/svg/HomeSvg.svg";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/component";
import RequestSummary from "@/common/request-summary/request-summary.common";

const CreateBooking = () => {
  //const t = useTranslations("CreateProfiles");
  const p = useTranslations("createBooking");
  const s = useTranslations("profileFooter");
  const theme = useTheme();
  const lang = useLocale();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const options = [
    {
      id: 0,
      option: p("myVenue"),
      description: p("selectVenueProfile"),
      icon: <VenueProfileSvg className="lg:!w-12 lg:!h-12  !w-[25px] !h-[25px]" />,
      path: "/select-profile",
    },
    {
      id: 1,
      option: p("commercialAddress"),
      description: p("actualAddress"),
      icon: <HomeSvg className="lg:!w-12 lg:!h-12  !w-[25px] !h-[25px]" />,
      path: "#",
    },
  ];
  const [selectedItem, setSelectedItem] = useState();
  const handleListItemClick = (item) => {
    setSelectedItem(item.id === selectedItem ? null : item);
  };
  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2">
        <ProfileNavbar
          pageNumber="1/7"
          tag={p("bookingRequest")}
          className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pt-24">
          <CreateProfileTitle title={isSmallScreen ? p("placementDetails") : p("eventTakePlace")}>
            <LocationSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <List sx={{ padding: 0 }} className="lg:!mt-12 md:!mt-12 !mb-28">
            {options.map((data) => (
              <Link key={data.id} href={data}>
                <ListItem
                  className={classNames(
                    "!border !rounded-[4px] cursor-pointer !max-w-[750px] !mb-4 !flex !justify-between lg:!p-6 md:!p-6 !p-4",
                    {
                      "border-[--inprogress-color]": selectedItem?.id === data.id,
                      "border-[--divider-color]": selectedItem?.id !== data.id,
                    },
                  )}
                  onClick={() => handleListItemClick(data)}
                >
                  <Box className="!flex !gap-x-4">
                    <ListItemIcon sx={{ minWidth: "1px" }}>
                      {selectedItem?.id === data.id ? (
                        <RadioButtonIcon className="!w-6 !h-6" />
                      ) : (
                        <RadioButtonUnchecked className=" !text-[--text-color]" />
                      )}
                    </ListItemIcon>
                    <Box>
                      <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
                        {data.option}
                      </Typography>
                      <Typography className="!text-[--text-color] !break-words !text-sm CraftworkGroteskRegular !pt-2">
                        {data.description}
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton sx={{ padding: 0 }} className="!mb-[18px] !w-12 !h-12">
                    {data?.icon}
                  </IconButton>
                </ListItem>
              </Link>
            ))}
          </List>
        </Box>
        <Box className="!flex justify-end !fixed !bottom-0 !h-[79px] lg:!max-w-[49%] !right-0 !bg-[--bg-color] !left-0 !border-t !border-t-[--divider-color] !z-20 !py-5 lg:!px-12 md:!px-12 !px-4">
          <Link
            href={selectedItem ? `/${lang}${selectedItem.path}` : ""}
            style={{ pointerEvents: selectedItem ? "auto" : "none" }}
          >
            <Button
              disabled={selectedItem === null ? true : false}
              className={classNames("!px-4 !py-2", {
                "!bg-[--disabled-color]": !selectedItem,
                "!bg-[--text-color]": selectedItem,
              })}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {s("Next")}
              </Typography>
              <East className="!text-[--bg-color] !ml-3" />
            </Button>
          </Link>
        </Box>
      </Box>
      <Box className="h-screen overflow-auto">
        <RequestSummary />
      </Box>
    </Box>
  );
};

export default CreateBooking;
