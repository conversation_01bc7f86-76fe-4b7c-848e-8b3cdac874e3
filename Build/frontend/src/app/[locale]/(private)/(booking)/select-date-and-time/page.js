"use client";
import { ProfileFooter } from "@/common/profile";
import RequestSummary from "@/common/request-summary/request-summary.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import React from "react";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { useLocale, useTranslations } from "next-intl";
import { useSelector } from "react-redux";
import BookingCalender from "@/component/calendar/booking-calender.component";

const SelectDateAndTime = () => {
  //const t = useTranslations("CreateProfiles");
  const p = useTranslations("createBooking");
  const s = useTranslations("profileFooter");
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { currentBookingStatus } = useSelector((state) => state.booking);
  const [selectedItem, setSelectedItem] = React.useState(true);
  return (
    <>
      <ProfileNavbar
        pageNumber="2/7"
        tag={p("bookingRequest")}
        className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
      >
        {" "}
        {/* <Typography
            className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline"
            onClick={() => {
              router.push(`/${lang}/contracts/contracts-by-me`);
              return;
            }}
          >
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
      </ProfileNavbar>
      <Box className="grid grid-cols-2">
        <Box className="lg:col-span-1 col-span-2  overflow-auto ">
          {/* <Box className="!hidden lg:!block">
            <Sidebar />
          </Box> */}

          {/* <Box className="!inline relative lg:!static lg:!hidden">
            <MobileNavbar />
          </Box> */}
          <Box className=" p-4 !pr-8 lg:!pr-8  !py-24">
            <CreateProfileTitle title={p("clickCalendar")}>
              <CalenderIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>

            <BookingCalender
              initialView={"timeGridWeek"}
              height={700}
              editable={true}
              selectable={true}
              profileId={currentBookingStatus?.otherProfileId}
              type="EVENT"
              setSelectedItem={setSelectedItem}
            />
          </Box>
          <Box className="fixed  left-0 right-0 w-full !z-[999]  lg:!hidden ">
            {/* just add !bottom-20 in className of this ProfileFooter component below line no -68 & UNCOMMENT <MobileNavbar /> box line no - 46 to 48 to render the mobile nav on this page. */}

            <ProfileFooter
              className="lg:!pl-28 lg:!px-2 !z-[999] "
              buttonName={isSmallScreen ? s("Next") : p("purchaserDetails")}
              disabled={selectedItem ? true : false}
              backurl={`/${lang}/user-data`}
              type="button"
              backurlType="router-back"
              footerType="booking"
            />
          </Box>
        </Box>
        <Box className="pb-[100px] lg:pb-[0px] lg:min-h-screen overflow-auto">
          <RequestSummary />
        </Box>
        <Box className="!hidden lg:!flex fixed !z-[999] !bottom-0 !left-0 !right-0 !w-full  ">
          <ProfileFooter
            className="lg:!pl-28 lg:!px-2 max-w-[49%] !z-[999]"
            buttonName={isSmallScreen ? s("Next") : p("purchaserDetails")}
            disabled={selectedItem ? true : false}
            backurl={`/${lang}/user-data`}
            type="button"
            backurlType="router-back"
            footerType="booking"
          />
        </Box>
      </Box>
    </>
  );
};

export default SelectDateAndTime;

//--------------------------------------------------------------------------------------------------------
// @without_layout & @footer_stays_in_bottom_in_all_pages

// "use client";
// import { ProfileFooter } from "@/common/profile";
// import RequestSummary from "@/common/request-summary/request-summary.common";
// import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
// import { Box, useMediaQuery, useTheme } from "@mui/material";
// import React from "react";
// import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
// import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
// import { useLocale, useTranslations } from "next-intl";
// import { useSelector } from "react-redux";
// import BookingCalender from "@/component/calendar/booking-calender.component";
// import Sidebar from "@/common/sidebar/sidebar.common";
// import MobileNavbar from "@/common/navbar/mobile-navbar.common";

// const SelectDateAndTime = () => {
//   //const t = useTranslations("CreateProfiles");
//   const p = useTranslations("createBooking");
//   const s = useTranslations("profileFooter");
//   const lang = useLocale();
//   const theme = useTheme();
//   const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
//   const { currentBookingStatus } = useSelector((state) => state.booking);
//   const [selectedItem, setSelectedItem] = React.useState(true);
//   return (
//     <>
//       <ProfileNavbar
//         pageNumber="2/7"
//         tag={p("bookingRequest")}
//         className=" h-[64px] lg:right-[50%] !bg-[--bg-color]"
//       >
//         {" "}
//         {/* <Typography
//             className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline"
//             onClick={() => {
//               router.push(`/${lang}/contracts/contracts-by-me`);
//               return;
//             }}
//           >
//             {t("saveUnpublished")}
//           </Typography>
//           <Close className="!text-[--text-color] !text-base" /> */}
//       </ProfileNavbar>
//       <Box className="grid grid-cols-2">
//         <Box className="lg:col-span-1 col-span-2  overflow-auto ">
//           <Box className="!hidden lg:!block">
//             <Sidebar />
//           </Box>
//           <Box className="!inline relative lg:!static lg:!hidden">
//             <MobileNavbar />
//           </Box>
//           <Box className="lg:ml-[100px] p-4 !pr-8 lg:!pr-8  !py-24">
//             <CreateProfileTitle title={p("clickCalendar")}>
//               <CalenderIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
//             </CreateProfileTitle>

//             <BookingCalender
//               initialView={"timeGridWeek"}
//               height={700}
//               editable={true}
//               selectable={true}
//               profileId={currentBookingStatus?.otherProfileId}
//               type="EVENT"
//               setSelectedItem={setSelectedItem}
//             />
//           </Box>
//           <Box className="fixed  left-0 right-0 w-full z-10  lg:!hidden ">
//             <ProfileFooter
//               className="lg:!pl-28 !bottom-20 lg:!px-2 z-[999] "
//               buttonName={isSmallScreen ? s("Next") : p("purchaserDetails")}
//               disabled={selectedItem ? true : false}
//               backurl={`/${lang}/user-data`}
//               type="button"
//               backurlType="router-back"
//               footerType="booking"
//             />
//           </Box>
//         </Box>
//         <Box className="pb-[100px] lg:pb-[0px] lg:min-h-screen overflow-auto">
//           <RequestSummary />
//         </Box>
//         <Box className="!hidden lg:!flex fixed !bottom-0 !left-0 !right-0 !w-full  ">
//           <ProfileFooter
//             className="lg:!pl-28 lg:!px-2 w-[50%]"
//             buttonName={isSmallScreen ? s("Next") : p("purchaserDetails")}
//             disabled={selectedItem ? true : false}
//             backurl={`/${lang}/user-data`}
//             type="button"
//             backurlType="router-back"
//             footerType="booking"
//           />
//         </Box>
//       </Box>
//     </>
//   );
// };

// export default SelectDateAndTime;
