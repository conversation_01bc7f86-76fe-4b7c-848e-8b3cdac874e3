"use client";
import EventCalender from "@/component/calendar/event-calendar.component";
import { useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { getUserCalender } from "@/store/slice/booking/booking.slice";
import { filterEvent } from "@/utils";
import Sidebar from "@/common/sidebar/sidebar.common";
import Navbar from "@/common/navbar/navbar.common";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import { Box } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);
const TIMEZONE = "America/Toronto";

const Event = () => {
  const dispatch = useDispatch();
  const [eventData, setEventData] = useState([]);
  const [originalData, setOriginalData] = useState([]);
  const [profileData, setProfileData] = useState([]);
  const [filters, setFilters] = useState({
    profileTypes: [],
    displayTypes: [],
    statusTypes: [],
  });

  const [startDate, setStartDate] = useState(
    dayjs().tz(TIMEZONE).startOf("week").add(1, "day").format("YYYY-MM-DD"),
  );
  const [period, setPeriod] = useState("WEEK");
  const [view, setView] = useState("dayGridMonth");
  const profileLabels = {
    ACT_PROFILE: "Act",
    VIRTUAL_ACT_PROFILE: "Virtual Act",
    VENUE_PROFILE: "Venue",
    VIRTUAL_VENUE_PROFILE: "Virtual Venue",
  };
  const getProfileData = (data) => {
    if (!data?.profileList) return [];

    // Create a Set to store unique profile types
    const uniqueProfiles = new Set();

    // Add profiles from profileList
    data.profileList.forEach((profile) => {
      uniqueProfiles.add({
        id: profile.profileId,
        name: profile.profileName,
        type: profileLabels[profile.profileType],
        profileImageUrls: profile.profileImageUrls,
      });
    });

    return Array.from(uniqueProfiles);
  };

  const fetchEvents = () => {
    dispatch(
      getUserCalender({
        startDate: dayjs(startDate).tz(TIMEZONE).startOf("day").toISOString(),
        period,
      }),
    )
      .unwrap()
      .then((response) => {
        setOriginalData(response.data); // Store original API response
        const filteredEvents = applyFilters(response.data);
        setProfileData(getProfileData(response.data));
        setEventData(filteredEvents);
      });
  };
  //console.log(eventData);
  useEffect(() => {
    fetchEvents();
  }, [startDate, period]);

  const applyFilters = (data) => {
    if (!data) return [];
    const allEvents = [];

    // Return all events if no filters are active
    if (
      filters.statusTypes.length === 0 &&
      filters.profileTypes.length === 0 &&
      filters.displayTypes.length === 0
    ) {
      data.profileList?.forEach((profile) => {
        profile.eventList.forEach((event) => {
          allEvents.push(
            filterEvent(
              event.scheduleTime,
              "profile",
              event?.eventName,
              event?.eventImageUrls,
              profile.profileType,
              event.status,
            ),
          );
        });
      });

      data.contractList?.forEach((contract) => {
        allEvents.push(
          filterEvent(
            contract.scheduleTime,
            "contract",
            "Contract",
            null,
            null,
            contract.contractState,
          ),
        );
      });

      data.specialEventList?.forEach((event) => {
        if (event.scheduleTime) {
          allEvents.push(
            filterEvent(
              event.scheduleTime,
              "profile",
              event?.eventName,
              event?.eventImageUrls,
              null,
              "ACTIVE",
            ),
          );
        }
      });
      return allEvents;
    }

    // Handle display type filters (own and favorite)
    if (filters.displayTypes.includes("own")) {
      data.profileList?.forEach((profile) => {
        if (profile.ownProfile === true) {
          profile.eventList.forEach((event) => {
            allEvents.push(
              filterEvent(
                event.scheduleTime,
                "profile",
                event?.eventName,
                event?.eventImageUrls,
                profile.profileType,
                event.status,
              ),
            );
          });
        }
      });
    }

    if (filters.displayTypes.includes("favorite")) {
      data.favoriteProfileList?.forEach((profile) => {
        profile.eventList.forEach((event) => {
          allEvents.push(
            filterEvent(
              event.scheduleTime,
              "favorite",
              event?.eventName,
              event?.eventImageUrls,
              profile.profileType,
              event.status,
            ),
          );
        });
      });
    }

    // Handle profile type filters
    if (filters.profileTypes.length > 0) {
      data.profileList?.forEach((profile) => {
        if (filters.profileTypes.includes(profile.profileId)) {
          profile.eventList.forEach((event) => {
            allEvents.push(
              filterEvent(
                event.scheduleTime,
                "profile",
                event?.eventName,
                event?.eventImageUrls,
                profile.profileType,
                event.status,
              ),
            );
          });
        }
      });

      // Handle special events
      data.specialEventList?.forEach((event) => {
        if (filters.profileTypes.includes(event.ownerProfileId)) {
          if (event.scheduleTime) {
            allEvents.push(
              filterEvent(
                event.scheduleTime,
                "profile",
                event?.eventName,
                event?.eventImageUrls,
                null,
                "ACTIVE",
              ),
            );
          }
        }
      });

      data.contractList?.forEach((contract) => {
        if (
          filters.profileTypes.includes(contract.actProfileId) ||
          filters.profileTypes.includes(contract.venueProfileId)
        ) {
          allEvents.push(
            filterEvent(
              contract.scheduleTime,
              "contract",
              "Contract",
              null,
              null,
              contract.contractState,
            ),
          );
        }
      });
    }

    // Handle status type filters
    if (filters.statusTypes.length > 0) {
      data.contractList?.forEach((contract) => {
        if (filters.statusTypes.includes(contract.contractState)) {
          allEvents.push(
            filterEvent(
              contract.scheduleTime,
              "contract",
              "Contract",
              null,
              null,
              contract.contractState,
            ),
          );
        }
      });
    }

    return allEvents;
  };

  // Add useEffect to handle filter changes
  useEffect(() => {
    if (originalData) {
      const filteredEvents = applyFilters(originalData);
      setEventData(filteredEvents);
    }
  }, [filters]); // Run effect when filters change

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleViewChange = (newView, currentDate) => {
    setView(newView);

    if (newView === "dayGridMonth") {
      setStartDate(
        dayjs(currentDate).tz(TIMEZONE).startOf("month").add(1, "day").format("YYYY-MM-DD"),
      );
      setPeriod("MONTH");
    } else if (newView === "timeGridWeek") {
      setStartDate(
        dayjs(currentDate).tz(TIMEZONE).startOf("week").add(1, "day").format("YYYY-MM-DD"),
      );
      setPeriod("WEEK");
    } else {
      setStartDate(dayjs(currentDate).tz(TIMEZONE).format("YYYY-MM-DD"));
      setPeriod("DAY");
    }
  };

  const goToPrev = () => {
    const newStartDate = dayjs(startDate)
      .tz(TIMEZONE) // Ensure timezone is set
      .subtract(1, period.toLowerCase())
      .toISOString(); // Convert back to ISO string

    setStartDate(newStartDate);
  };

  const goToNext = () => {
    const newStartDate = dayjs(startDate)
      .tz(TIMEZONE) // Ensure timezone is set
      .add(1, period.toLowerCase())
      .toISOString(); // Convert back to ISO string

    setStartDate(newStartDate);
  };

  // useEffect(() => {
  //   dispatch(getUserCalender({ startDate: dayjs(startDate).startOf("day").toISOString(), period }))
  //     .unwrap()
  //     .then((response) => {
  //       const allEvents = [];

  //       // Extract events from profileList
  //       response?.data?.profileList?.forEach((profile) => {
  //         profile.eventList.forEach((event) => {
  //           allEvents.push(
  //             filterEvent(event.scheduleTime, "profile", event?.eventName, [
  //               "http://18.220.174.46:8080/api/v1/public/act/profiles/390961e5-6f29-49b1-ba32-2c6ebc3842cc/media/mars.jpeg",
  //             ]),
  //           );
  //         });
  //       });

  //       // Extract events from favoriteProfileList
  //       response?.data?.favoriteProfileList?.forEach((profile) => {
  //         profile.eventList.forEach((event) => {
  //           allEvents.push(
  //             filterEvent(event.scheduleTime, "favorite", event?.eventName, [
  //               "http://18.220.174.46:8080/api/v1/public/act/profiles/390961e5-6f29-49b1-ba32-2c6ebc3842cc/media/mars.jpeg",
  //             ]),
  //           );
  //         });
  //       });

  //       // Extract events from contractList
  //       response?.data?.contractList?.forEach((contract) => {
  //         allEvents.push(
  //           filterEvent(contract.scheduleTime, "contract", "Contract", [
  //             "http://18.220.174.46:8080/api/v1/public/act/profiles/390961e5-6f29-49b1-ba32-2c6ebc3842cc/media/mars.jpeg",
  //           ]),
  //         );
  //       });

  //       setEventData(allEvents);
  //       // const data = responseDate.map((item) => {
  //       //   return filterEvent(item, editable);
  //       // });
  //     });
  // }, []);

  return (
    <>
      <Box className="lg:!fixed lg:!z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      {/* <MobileViewTitle title="Search" /> */}
      <Box className="!flex !pb-24 lg:!pb-0">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <Box className="lg:!pl-36 !pl-4 lg:!pt-24 !w-full">
          <EventCalender
            initialView={view}
            events={eventData}
            height={600}
            seleselectable={true}
            onNext={goToNext}
            onPrev={goToPrev}
            onViewChange={handleViewChange}
            handleFilterChange={handleFilterChange}
            profileData={profileData}
          />
        </Box>
      </Box>
    </>
  );
};
export default Event;
