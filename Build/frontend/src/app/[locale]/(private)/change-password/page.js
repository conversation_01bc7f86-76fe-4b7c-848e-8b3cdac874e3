"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import PasswordRecoveryPng from "@/assets/png/PasswordRecovery.png";
import { CommonImage } from "@/component";
import CloseIcon from "@mui/icons-material/Close";
import ChangePasswordForm from "@/containers/change-password/change-password.container";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import Link from "next/link";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";
import { useSelector } from "react-redux";
import { notFound } from "next/navigation";

const ChangePassword = ({ params }) => {
  const t = useTranslations("changePassword");
  const s = useTranslations("settings.settingsInfo");
  const p = useTranslations("leaveFeedback");
  const { token, currentUser } = useSelector((state) => state.login);
  if (currentUser?.socialLoginUser) {
    notFound();
  }
  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="hidden lg:block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={PasswordRecoveryPng}
          alt="change-password"
          className="!w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className="lg:basis-1/2 w-full relative flex-grow flex flex-col justify-between">
        <Box className=" !border-b !border-b-[--divider-color] !px-5 !py-4 !flex items-center !justify-between">
          <Link href={token ? `/${params.locale}/search` : `/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
          <Link href={`/${params.locale}/dashboard`}>
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold !cursor-pointer">
              {p("cancel")} <CloseIcon className="!text-base" />
            </Typography>
          </Link>
        </Box>
        <Box className="!max-w-[350px] !m-auto lg:!mt-[15vh] !px-4 !lg:!px-0 !mt-20 !flex-grow">
          <Typography className="!text-[--text-color] CraftworkGroteskMedium !mb-6 !text-2xl !text-center">
            {s("changePassword")}
          </Typography>
          <Typography className="!text-[--text-color] Sora300 !mb-6 !text-sm !text-center">
            {t("changeIt")}
          </Typography>
          <ChangePasswordForm />
        </Box>
        <DesktopFooter className="mx-auto !mb-6" />
      </Box>
    </Box>
  );
};

export default ChangePassword;
