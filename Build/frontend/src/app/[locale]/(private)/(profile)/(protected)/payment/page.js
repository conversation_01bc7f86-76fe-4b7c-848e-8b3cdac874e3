"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, Typography } from "@mui/material";
import React from "react";
import PaymentSvg from "@/assets/svg/act-type.svg/PaymentSvg.svg";
import ActPaymentForm from "@/containers/act-forms/act-payment/act.payment.containers";

import ActPreview from "@/common/(act)/act-preview/act.preview.common";
import { Close } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";

const ActPayment = () => {
  const t = useTranslations("actPayment");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("venue");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="7/7"
          tag={t("createProfile")}
          className=" h-[64px] lg:right-[50%]"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpuplished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pb-28 !max-w-[650px] !pt-24 ">
          <CreateProfileTitle
            title={previewData?.profile?.option === s("Act") ? t("actPayment") : p("venuePayment")}
          >
            <PaymentSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <ActPaymentForm />
        </Box>
      </Box>
      {/* Act Preview */}
      <ActPreview />
    </Box>
  );
};

export default ActPayment;
