"use client";
import React from "react";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Box, Typography } from "@mui/material";

import MusicGenreIcon from "@/assets/svg/act-type.svg/MusicGenre.svg";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";

import MusicGenerePreview from "@/common/(act)/music-genre-preview/music-genre-preview.common";
import MusicGenreForm from "@/containers/act-forms/music-genre/music-genre.container";
import { Close } from "@mui/icons-material";
import { useTranslations } from "next-intl";

const MusicGenre = () => {
  const t = useTranslations("musicGenre");
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="5/7"
          tag={t("createProfile")}
          className=" h-[64px] lg:right-[50%]"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpuplished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pb-28 !max-w-[750px] !pt-24 ">
          <CreateProfileTitle title={t("musicGenre")}>
            <MusicGenreIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          {/** music genre container */}
          <MusicGenreForm />
        </Box>
      </Box>
      {/** genere preview screen */}
      <MusicGenerePreview />
    </Box>
  );
};

export default MusicGenre;
