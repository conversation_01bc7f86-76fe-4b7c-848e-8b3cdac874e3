"use client";

import { <PERSON>, Button, Typography } from "@mui/material";
import EventForm from "./add-special-event/page";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Add, ArrowBack } from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { getAllSpecialEventsAction } from "@/store/slice/specialevent/event.slice";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import EventCard from "@/common/profile-card/event-card.common ";
import usePaginate from "@/hooks/usePaginate";
import Paginate from "@/containers/paginate/paginate.container";
import { Loader } from "@/component";
import SpecialEventsLayout from "./specialEventsLayout";

const SpecialEvents = () => {
  const t = useTranslations("specialEvents");
  const [isShow, setIsShow] = useState(false);
  const [actPhotos, setActPhotos] = useState([]); // Add this line
  const lang = useLocale();
  const router = useRouter();

  const searchParams = useSearchParams();

  const { pageNo } = usePaginate();
  const [pageSize] = useState(12);

  const proId = useParams("profileId").id;
  const handleAddSpecialEvent = () => {
    setIsShow((prev) => !prev);
    setActPhotos([]);
    router.push(`/${lang}/${proId}/special-events`);
  };
  const dispatch = useDispatch();
  const getdata = async () => {
    await dispatch(
      getAllSpecialEventsAction({
        proId,
        page: pageNo - 1, // Convert 1-based to 0-based pagination
        size: pageSize,
      }),
    ).unwrap();
  };

  useEffect(() => {
    const eventId = searchParams.get("eventId");

    if (eventId) setIsShow(true); // auto-show form if editing
  }, []);

  useEffect(() => {
    getdata();
  }, [proId, isShow, pageNo]);

  const { data, loading } = useSelector((state) => state.events) || {};
  const specialEventsData = data?.content;
  if (loading) {
    <Loader />;
  }
  return (
    <Box className="lg:pl-[310px] ">
      {/* <SaveBackButtonContainers loading={false} /> */}
      <SpecialEventsLayout />
      <Box className="!ml-auto lg:!mt-[80px] MuiBox-root css-0 w-[100%]">
        <Box
          sx={{
            position: "fixed",
            top: "64px",
            left: "0",
            right: "0",
            width: "100%",
            backgroundColor: "#181B1B",
            zIndex: 10,
            padding: "8px 16px",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button
            onClick={handleAddSpecialEvent}
            className="!gap-x-2 !py-2 !px-4  "
            sx={{
              backgroundColor: "white !important",
              boxShadow: "none",

              width: "auto",

              display: "flex",
              justifyContent: "flex-end",
              "&:hover": {
                backgroundColor: "#d2d5d9 !important",
              },
            }}
          >
            {isShow ? (
              <>
                <ArrowBack className="!text-[--bg-color]" />
                <Typography className="!text-sm text-[--bg-color] font-craftWorkHeavy">
                  List Special Events
                </Typography>
              </>
            ) : (
              <>
                <Typography className="!text-sm text-[--bg-color] font-craftWorkHeavy">
                  {t("addSpecialEvent") || "Add Special Event"}
                </Typography>
                <Add className="!text-[--bg-color] !text-2xl" />
              </>
            )}
          </Button>
        </Box>

        {/* Conditionally Render Event Cards or Form */}
        {!isShow ? (
          <Box className="flex flex-col lg:ml-12 ">
            <Box className="flex flex-wrap  mt-28 md:mt-32 lg:mt-16  lg:gap-4 p-[-20px]">
              {specialEventsData?.map((event) => (
                <EventCard
                  type="specialEvent"
                  profileTypeselecthandler={() => {
                    /* handle event selection here if needed */
                  }}
                  key={event.specialEventId}
                  profiles={{
                    content: [
                      {
                        eventId: event.specialEventId,
                        eventName: event.eventName,
                        eventMediaInfo: {
                          imageUrls: event.eventImageUrls,
                        },
                        venueName: event.ownerName,
                        venueImageUrls: event.ownerImageUrls,
                        scheduleTime: event.scheduleTime,
                      },
                    ],
                  }}
                  setShowForm={setIsShow}
                  getdata={getdata}
                />
              ))}
            </Box>
            {data?.totalPages > 1 && (
              <Box className="flex justify-center mt-4 mb-8">
                <Paginate totalRecords={data.totalElements} perPageRecord={data.size} />
              </Box>
            )}
          </Box>
        ) : (
          <EventForm setIsShow={setIsShow} actPhotos={actPhotos} setActPhotos={setActPhotos} />
        )}
      </Box>
    </Box>
  );
};

export default SpecialEvents;
