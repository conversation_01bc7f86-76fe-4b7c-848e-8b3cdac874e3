"use client";
import { Button, CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";
import FeedbackImage from "@/assets/png/FeedbackImage.png";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Clear } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import LeaveFeedbackForms from "@/containers/leave-feedback/leave-feedback.containers";
import { useDispatch } from "react-redux";
import { getFeedbackById } from "@/store/slice/act/act.slice";
import Loader from "@/component/loader/loader.component";
import { useRouter } from "next/navigation";

const LeaveAFeedback = ({ params }) => {
  const t = useTranslations("leaveFeedback");
  const feedbackId = params.id;
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(true);
  const [feedbackData, setFeedbackData] = React.useState({});
  const router = useRouter();
  useEffect(() => {
    dispatch(getFeedbackById(feedbackId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          setFeedbackData(response.data.data);
        }
      })
      .catch(() => {});
  }, []);

  // useEffect(() => {
  //   const page = 0;
  //   const size = 100;
  //   const params = stringifyParams({ page, size });
  //   dispatch(getProfilesCurrent(params))
  //     .unwrap()
  //     .then((response) => {
  //       if (response.status === 200) {
  //         const data =
  //           response.data.data.content.length > 0 &&
  //           response.data.data.content.map((item) => {
  //             return { value: item.profileId, label: item.profileName };
  //           });
  //         setProfileData(data ?? []);
  //         setLoading(false);
  //       }
  //     })
  //     .catch(() => {});
  // }, []);

  if (loading) {
    return <Loader />;
  }

  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="lg:basis-1/2 w-full ">
        <ProfileNavbar
          tag={t("leaveFeedback")}
          className=" h-[64px] !fixed !top-0 lg:!right-[50%] !right-0 !left-0 border border-[--divider-color] !bg-[--bg-color] !z-20"
        >
          <Button
            onClick={() => router.back()}
            className=" flex gap-x-1"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy underline !text-sm !leading-[15.4px] !text-[--text-color]">
              {t("cancel")}
            </Typography>
            <Clear className="text-xl" />
          </Button>
        </ProfileNavbar>
        <LeaveFeedbackForms
          feedbackId={feedbackId}
          setLoading={setLoading}
          loading={loading}
          feedbackData={feedbackData}
        />
      </Box>
      <Box className="!hidden lg:!block lg:basis-1/2">
        <CommonImage
          src={FeedbackImage}
          alt="leave-a-feedbck"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
    </Box>
  );
};

export default LeaveAFeedback;
