"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Field, Typo<PERSON>, IconButton } from "@mui/material";
import { LocalizationProvider, MobileDateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useForm, Controller } from "react-hook-form";
import { useDropzone } from "react-dropzone";
import classNames from "classnames";
import { Dropdown } from "@/component/form";
import { showSnackbar } from "@/utils/snackbar.utils";
import { cn } from "@/lib/cn";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import PhotoIcon from "@/assets/svg/act-type.svg/PhotoIcon.svg";
import DragAndDropIcon from "@/assets/svg/act-type.svg/DragAndDrop.svg";
import DeleteIcon from "@/assets/svg/DeleteIcon.svg";
import { <PERSON><PERSON>, CommonImage, Loader } from "@/component";
import timezones from "@/component/calendar/timezones";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import {
  addNewSpecialEventAction,
  createEventMedia,
  deleteEventMedia,
  getSpecialEventByIdAction,
  updateSpecialEventAction,
} from "@/store/slice/specialevent/event.slice";
import { useDispatch } from "react-redux";
import NoteList from "@/ui/note-list/note-list.ui";
import EditSidebar from "../../edit-sidebar/page";
import { useLocale } from "next-intl";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { EVENT_CONSTANTS } from "@/validation/auth/constants";

dayjs.extend(duration);

const eventFormValidation = yup.object().shape({
  eventName: yup
    .string()
    .required("Event name is required")
    .min(3, "Event name must be at least 3 characters"),
  aboutTheEvent: yup.string().required("About the event is required"),
  scheduleTime: yup
    .object()
    .shape({
      startDate: yup.date().required("Start date is required").typeError("Invalid start date"),
      endDate: yup
        .date()
        .required("End date is required")
        .typeError("Invalid end date")
        .test("is-after-start", "End date must be after start date", function (value) {
          const { startDate } = this.parent;
          if (!startDate || !value) return true;
          return dayjs(value).isAfter(dayjs(startDate));
        }),
      timeZone: yup.string().required("Timezone is required"),
      recurring: yup.boolean(),
      recurrence: yup.mixed().when("recurring", {
        is: true,
        then: () =>
          yup.object().shape({
            recurrenceType: yup
              .string()
              .required("Recurrence type is required")
              .oneOf(["WEEKLY", "MONTHLY", "BI_WEEKLY"], "Invalid recurrence type"),
            daysOfWeek: yup.array().when("recurrenceType", {
              is: (type) => type === "WEEKLY" || type === "BI_WEEKLY",
              then: () =>
                yup
                  .array()
                  .of(
                    yup
                      .string()
                      .oneOf([
                        "MONDAY",
                        "TUESDAY",
                        "WEDNESDAY",
                        "THURSDAY",
                        "FRIDAY",
                        "SATURDAY",
                        "SUNDAY",
                      ]),
                  )
                  .min(1, "Select at least one day")
                  .required("Days of week are required"),
            }),
            recurrenceEndType: yup.object().shape({
              endDate: yup
                .date()
                .required("Recurrence end date is required")
                .test("is-after-event-end", "Must be after event end date", function (value) {
                  try {
                    const eventEndDate =
                      this.from &&
                      this.from[1] &&
                      this.from[1].value &&
                      this.from[1].value.scheduleTime &&
                      this.from[1].value.scheduleTime.endDate;

                    if (!value || !eventEndDate) return true;
                    return dayjs(value).isAfter(dayjs(eventEndDate));
                  } catch (error) {
                    return false;
                  }
                }),
            }),
          }),
      }),
    })
    .test("validate-recurrence-dates", "Invalid date range", function (value) {
      if (!value.recurring) return true;

      const eventEnd = value.endDate;
      const recurrenceEnd = value.recurrence?.recurrenceEndType?.endDate;

      if (!eventEnd || !recurrenceEnd) return true;

      if (!dayjs(recurrenceEnd).isAfter(dayjs(eventEnd))) {
        return this.createError({
          path: "recurrence.recurrenceEndType.endDate",
          message: "Recurrence end date must be after event end date",
        });
      }

      return true;
    }),
  eventImageUrls: yup.array(),
});

const EventForm = ({ setIsShow, actPhotos, setActPhotos }) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [durationInHours, setDurationInHours] = useState(0);
  const [isLoadingForm, setIsLoadingForm] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [createdEventId, setCreatedEventId] = useState(null);
  const lang = useLocale();
  const profileId = useParams().id;
  const dispatch = useDispatch();

  const days = [
    { day: "Mo", name: "MONDAY" },
    { day: "Tu", name: "TUESDAY" },
    { day: "We", name: "WEDNESDAY" },
    { day: "Th", name: "THURSDAY" },
    { day: "Fr", name: "FRIDAY" },
    { day: "Sa", name: "SATURDAY" },
    { day: "Su", name: "SUNDAY" },
  ];

  const selectedDate = ["Single Date", "Recurring Date"];
  const recurrenceTypeData = ["WEEKLY", "BI_WEEKLY", "MONTHLY"];

  const defaultScheduleTime = {
    profileType: "ALL",
    scheduleName: "EVENT",
    scheduleDescription: "",
    scheduleType: "SPECIAL_EVENT",
    timeZone: "(GMT-04:00) Eastern Time - Toronto",
    startDate: dayjs().format(),
    startTime: dayjs().format(),
    endDate: dayjs().add(1, "hour").format(),
    endTime: dayjs().add(1, "hour").format(),
    recurring: false,
    recurrence: {
      recurrenceType: "WEEKLY",
      interval: 0,
      count: 0,
      endDate: "",
      daysOfWeek: ["MONDAY"],
      daysOfMonth: 0,
      monthsOfYear: ["JANUARY"],
      recurrenceEndType: {
        never: false,
        endDate: null,
        occurrences: 0,
      },
    },
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    control,
    reset,
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      ...defaultScheduleTime,
      scheduleTime: {
        ...defaultScheduleTime.scheduleTime,
        startDate: dayjs(),
        endDate: dayjs().add(1, "hour"),
      },
    },
    mode: "onChange",
    resolver: yupResolver(eventFormValidation),
  });

  const startDate = watch("scheduleTime.startDate");
  const endDate = watch("scheduleTime.endDate");

  const searchParams = useSearchParams();
  const eventId = searchParams.get("eventId");

  const setFormValues = (eventData) => {
    const scheduleTime = eventData.scheduleTime
      ? {
          ...defaultScheduleTime,
          ...eventData.scheduleTime,
          startDate: dayjs(
            eventData.scheduleTime.startDate ||
              eventData.scheduleTime.startTime ||
              defaultScheduleTime.startDate,
          ),
          endDate: dayjs(
            eventData.scheduleTime.endDate ||
              eventData.scheduleTime.endTime ||
              defaultScheduleTime.endDate,
          ),
          timeZone: eventData.scheduleTime.timeZone || defaultScheduleTime.timeZone,
          recurring: eventData.scheduleTime.recurring || defaultScheduleTime.recurring,
          recurrence: eventData.scheduleTime.recurring
            ? {
                ...defaultScheduleTime.recurrence,
                ...eventData.scheduleTime.recurrence,
                daysOfWeek:
                  eventData.scheduleTime.recurrence?.daysOfWeek ||
                  defaultScheduleTime.recurrence.daysOfWeek,
                recurrenceType:
                  eventData.scheduleTime.recurrence?.recurrenceType ||
                  defaultScheduleTime.recurrence.recurrenceType,
              }
            : defaultScheduleTime.recurrence,
        }
      : defaultScheduleTime;

    reset({
      eventName: eventData.eventName || "",
      aboutTheEvent: eventData.aboutTheEvent || "",
      scheduleTime,
      eventImageUrls: eventData.eventImageUrls || [],
      ownerProfileId: profileId || "",
      ownerImageUrls: eventData.ownerImageUrls || [],
      ownerName: eventData.ownerName || "",
    });

    if (eventData.eventImageUrls?.length) {
      setActPhotos(eventData.eventImageUrls);
    }
  };

  useEffect(() => {
    if (eventId) {
      setIsLoadingForm(true);
      dispatch(getSpecialEventByIdAction(eventId))
        .unwrap()
        .then((response) => {
          if (response) {
            setFormValues(response);
            setCreatedEventId(eventId); // Add this line to set createdEventId in edit mode
          } else {
            router.push(`/${lang}/${profileId}/special-events`);
          }
        })
        .catch((error) => {
          showSnackbar(error.message || "Error loading event", "error");
          router.push(`/${lang}/${profileId}/special-events`);
        })
        .finally(() => {
          setIsLoadingForm(false);
        });
    }
  }, [eventId, dispatch, router, lang, profileId]);

  useEffect(() => {
    try {
      const start = dayjs(startDate);
      const end = dayjs(endDate);

      if (start.isValid() && end.isValid()) {
        const durationHours = end.diff(start, "hour", true);
        const duration = Math.max(0, parseFloat(durationHours.toFixed(2)));
        setDurationInHours(duration);
      } else {
        setDurationInHours(0);
      }
    } catch (error) {
      setDurationInHours(0);
    }
  }, [startDate, endDate]);

  const imageNotes = [
    "Photo size should be less than 25MB",
    "You can upload up to 10 photos",
    "Perfect size is 1080 x 1080 px",
  ];

  useEffect(() => {
    setValue("eventImageUrls", actPhotos, {
      shouldDirty: true, // This will mark the form as dirty when photos change
    });
  }, [actPhotos, setValue]);

  const { getRootProps, getInputProps } = useDropzone({
    accept: { "image/*": [".jpeg", ".png", ".jpg"] },
    onDrop: (acceptedFiles) => {
      for (let i = 0; i < acceptedFiles.length; i++) {
        const formData = new FormData();
        const file = acceptedFiles[i];
        formData.append("file", file);
        handleUploadMedia(formData, profileId);
      }
    },
  });

  const handleUploadMedia = (formData, profileId) => {
    setIsSubmitting(true);
    dispatch(createEventMedia({ file: formData, profileId, specialEventId: createdEventId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setIsSubmitting(false);
          setActPhotos((prevPhotos) => [...prevPhotos, response.data.data]);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        setIsSubmitting(false);
        showSnackbar(error, "error");
      });
  };

  const handleDeleteImage = (event, fileUrl) => {
    event.stopPropagation();
    event.preventDefault();

    const fileName = fileUrl.split("/").pop();
    const updatedPhotos = actPhotos.filter((photo) => photo !== fileUrl);

    setIsSubmitting(true);
    dispatch(deleteEventMedia({ fileName, profileId, specialEventId: createdEventId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setIsSubmitting(false);
          setActPhotos(updatedPhotos);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        setIsSubmitting(false);
        showSnackbar(error, "error");
      });
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);

    try {
      const formattedData = {
        eventName: data.eventName,
        aboutTheEvent: data.aboutTheEvent,
        scheduleTime: {
          ...defaultScheduleTime,
          timeZone: data.scheduleTime.timeZone,
          startDate: dayjs(data.scheduleTime.startDate).format(),
          startTime: dayjs(data.scheduleTime.startDate).format(),
          endDate: dayjs(data.scheduleTime.endDate).format(),
          endTime: dayjs(data.scheduleTime.endDate).format(),
          recurring: data.scheduleTime.recurring,
          recurrence: data.scheduleTime.recurring
            ? {
                ...defaultScheduleTime.recurrence,
                recurrenceType: data.scheduleTime.recurrence.recurrenceType,
                daysOfWeek: data.scheduleTime.recurrence.daysOfWeek,
                endDate: data.scheduleTime.recurrence.endDate
                  ? dayjs(data.scheduleTime.recurrence.endDate).format()
                  : null,
                recurrenceEndType: {
                  never: false,
                  endDate: data.scheduleTime.recurrence.recurrenceEndType.endDate
                    ? dayjs(data.scheduleTime.recurrence.recurrenceEndType.endDate).format()
                    : null,
                  occurrences: 0,
                },
              }
            : null,
        },
        eventImageUrls: [],
        ownerProfileId: profileId,
        ownerImageUrls: [],
        ownerName: "",
      };

      if (!eventId && currentStep === 1) {
        const response = await dispatch(addNewSpecialEventAction(formattedData)).unwrap();
        if (response) {
          setCreatedEventId(response.data.specialEventId);
          setCurrentStep(2);
        }
      } else {
        // For edit mode or final step of add mode
        if (eventId) {
          await dispatch(
            updateSpecialEventAction({
              eventId: eventId,
              data: formattedData,
            }),
          ).unwrap();
        }
        showSnackbar(
          eventId ? "Event updated successfully" : "Event published successfully",
          "success",
        );
        setIsShow(false);
        router.push(`/${lang}/${profileId}/special-events`);
      }
    } catch (error) {
      showSnackbar(error || "Error saving event", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getButtonText = () => {
    if (isSubmitting) return "Please Wait...";
    if (!eventId && currentStep === 1) return "Next";
    if (eventId) return "Update Event";
    return "Publish";
  };

  if (isLoadingForm) {
    return <Loader />;
  }

  return (
    <>
      <div>
        <Box className="lg:block hidden">
          <EditSidebar />
        </Box>
      </div>

      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        className="w-full max-w-[64rem] mx-auto  "
        sx={{ bgcolor: "", color: "white", p: 3 }}
      >
        <Box className="flex flex-col mt-24 lg:mt-16  md:mt-28  md:flex-row items-start md:items-center gap-4 mb-6">
          <Typography
            component="label"
            htmlFor="eventName"
            className="text-white font-medium w-full md:w-[150px]"
          >
            Event Name
          </Typography>
          <TextField
            id="eventName"
            {...register("eventName", { required: "Event name is required" })}
            fullWidth
            size="small"
            inputProps={{
              maxLength: EVENT_CONSTANTS.EVENT_NAME.MAX_LENGTH,
            }}
            error={!!errors.eventName}
            helperText={errors.eventName?.message}
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                backgroundColor: "transparent",
                "& fieldset": {
                  borderColor: "white",
                },
                "&:hover fieldset": {
                  borderColor: "white",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "white",
                },
              },
              "& .MuiFormHelperText-root": {
                color: "#f44336",
              },
            }}
          />
        </Box>

        <Box className="flex flex-col md:flex-row items-start gap-4 mb-6">
          <Typography
            component="label"
            htmlFor="aboutTheEvent"
            className="text-white font-medium w-full md:w-[150px] pt-2"
          >
            About the Event
          </Typography>
          <TextField
            id="aboutTheEvent"
            {...register("aboutTheEvent", { required: "Description is required" })}
            multiline
            rows={5}
            fullWidth
            inputProps={{
              maxLength: EVENT_CONSTANTS.ABOUT_EVENT.MAX_LENGTH,
            }}
            error={!!errors.aboutTheEvent}
            helperText={errors.aboutTheEvent?.message}
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                backgroundColor: "transparent",
                "& fieldset": {
                  borderColor: "white",
                },
                "&:hover fieldset": {
                  borderColor: "white",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "white",
                },
              },
              "& .MuiFormHelperText-root": {
                color: "#f44336",
              },
            }}
          />
        </Box>

        <Box className="flex flex-col md:flex-row items-start gap-4 mb-6">
          <Typography component="label" className="text-white font-medium w-full md:w-[150px] pt-2">
            Schedule
          </Typography>
          <Box sx={{ bgcolor: "#1E1E1E", p: 3, borderRadius: "4px", width: "100%" }}>
            <Box className="space-y-2 mb-4">
              <Box className="flex items-center gap-2">
                <Typography variant="h6" className="text-white">
                  Date and time
                </Typography>
              </Box>
              <Typography className="text-gray-300 text-sm">
                Select the perfect date and time so attendees mark their calendars, eagerly
                anticipating your event.
              </Typography>
            </Box>

            <Box className="flex gap-2 py-2 items-center">
              {selectedDate.map((data, index) => (
                <Controller
                  name="scheduleTime.recurring"
                  control={control}
                  key={index}
                  render={({ field }) => (
                    <Typography
                      className={
                        (field?.value && data === "Recurring Date") ||
                        (!field?.value && data === "Single Date")
                          ? "border text-[#4caf50] cursor-pointer text-center border-[#4caf50] rounded-[4px] py-2 px-4"
                          : "border text-white text-center cursor-pointer border-[#666] rounded-[4px] py-2 px-4"
                      }
                      onClick={() => {
                        if (data === "Recurring Date") {
                          setValue("scheduleTime.recurring", true, {
                            shouldDirty: true,
                          });
                        } else {
                          setValue("scheduleTime.recurring", false, {
                            shouldDirty: true,
                          });
                        }
                      }}
                    >
                      {data}
                    </Typography>
                  )}
                />
              ))}
            </Box>

            <Box className="py-2">
              <Typography className="text-sm text-gray-300 py-2">Time Zone</Typography>
              <Box className="border border-[#666] w-full rounded-[4px]">
                <Controller
                  name="scheduleTime.timeZone"
                  control={control}
                  defaultValue={defaultScheduleTime.timeZone}
                  render={({ field }) => (
                    <Dropdown
                      options={timezones}
                      selectedValue={field.value || defaultScheduleTime.timeZone}
                      onSelect={(value) => {
                        field.onChange(value);
                      }}
                      className="w-full !text-white"
                    />
                  )}
                />
              </Box>
            </Box>

            <Box className="flex gap-3 lg:flex-row flex-col py-3 items-start">
              <Box className="w-full">
                <Typography className="text-sm pb-2 text-gray-300">Start Date & Time</Typography>

                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <Controller
                    name="scheduleTime.startDate"
                    control={control}
                    defaultValue={dayjs()}
                    render={({ field }) => (
                      <MobileDateTimePicker
                        value={dayjs(field.value)}
                        onChange={(date) => {
                          field.onChange(date);
                          setValue("scheduleTime.startDate", date);
                        }}
                        className="border w-full border-[#666]"
                        sx={{
                          "& .MuiInputBase-root": {
                            color: "white",
                            border: "1px solid #666",
                            bgcolor: "#333",
                          },
                        }}
                      />
                    )}
                  />
                </LocalizationProvider>

                <Box className="w-full mt-4">
                  <Typography className="text-sm pb-2 text-gray-300">Duration</Typography>
                  <TextField
                    value={`${durationInHours} hours`}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="w-full border border-[#666] rounded-[2px]"
                    sx={{
                      "& input::placeholder": {
                        color: "#EFEFEF",
                        border: 0,
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        bgcolor: "#333",
                        "& fieldset": {
                          borderWidth: 0,
                        },
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                    }}
                  />
                </Box>
              </Box>
              <Box className="w-full">
                <Typography className="text-sm pb-2 text-gray-300">End Date & Time</Typography>

                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <Controller
                    name="scheduleTime.endDate"
                    control={control}
                    render={({ field }) => (
                      <MobileDateTimePicker
                        value={dayjs(field.value)}
                        onChange={(date) => {
                          field.onChange(date);
                          setValue("scheduleTime.endDate", date);
                        }}
                        minDateTime={dayjs(watch("scheduleTime.startDate"))}
                        className="border w-full border-[#666]"
                        sx={{
                          "& .MuiInputBase-root": {
                            color: "white",
                            border: "1px solid #666",
                            bgcolor: "#333",
                          },
                        }}
                      />
                    )}
                  />
                </LocalizationProvider>

                {watch("scheduleTime.recurring") && (
                  <Box className="!border !border-[#666] !rounded-[2px] !mt-2 !py-1">
                    <Controller
                      name="scheduleTime.recurrence.recurrenceType"
                      control={control}
                      defaultValue={"WEEKLY"}
                      render={({ field }) => (
                        <Dropdown
                          onSelect={field.onChange}
                          options={recurrenceTypeData || []}
                          selectedValue={field.value || "WEEKLY"}
                          className="!text-white !w-full"
                        />
                      )}
                    />
                  </Box>
                )}

                {watch("scheduleTime.recurring") &&
                  watch("scheduleTime.recurrence.recurrenceType") !== "MONTHLY" && (
                    <Box className="flex flex-wrap items-center gap-2 my-2">
                      {days.map((day, index) => (
                        <Controller
                          name="scheduleTime.recurrence.daysOfWeek"
                          control={control}
                          defaultValue={["MONDAY"]}
                          key={index}
                          render={({ field }) => (
                            <Box
                              className={classNames(
                                "w-[37px] h-[37px] flex items-center justify-center border rounded-full",
                                {
                                  "border-[#4caf50]": field.value.includes(day.name),
                                  "border-[#666]": !field.value.includes(day.name),
                                },
                              )}
                              onClick={() => {
                                const days = [...field.value];
                                if (days.includes(day.name)) {
                                  const index = days.indexOf(day.name);
                                  days.splice(index, 1);
                                } else {
                                  days.push(day.name);
                                }
                                field.onChange(days);
                              }}
                              style={{ cursor: "pointer" }}
                            >
                              <Typography
                                className={classNames("text-sm", {
                                  "text-[#4caf50]": field.value.includes(day.name),
                                  "text-white": !field.value.includes(day.name),
                                })}
                              >
                                {day.day}
                              </Typography>
                            </Box>
                          )}
                        />
                      ))}
                    </Box>
                  )}

                {errors?.scheduleTime?.endDate && (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors?.scheduleTime?.endDate?.message}
                  </Typography>
                )}

                {watch("scheduleTime.recurring") && (
                  <Box className="mt-4">
                    <Typography className="text-sm text-gray-300 pb-2">Repeat until</Typography>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <Controller
                        name="scheduleTime.recurrence.recurrenceEndType.endDate"
                        control={control}
                        defaultValue={null}
                        render={({ field, fieldState: { error } }) => (
                          <Box>
                            <MobileDateTimePicker
                              value={field.value ? dayjs(field.value) : null}
                              onChange={(date) => field.onChange(date)}
                              minDateTime={dayjs(watch("scheduleTime.endDate"))}
                              className="border w-full border-[#666]"
                              sx={{
                                "& .MuiInputBase-root": {
                                  color: "white",
                                  border: "1px solid #666",
                                  bgcolor: "#333",
                                },
                              }}
                            />
                            {error && (
                              <Typography className="text-red-500 text-sm mt-1">
                                {error.message}
                              </Typography>
                            )}
                          </Box>
                        )}
                      />
                    </LocalizationProvider>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* Show media section immediately in edit mode, or after step 1 in add mode */}
        {(eventId || currentStep === 2) && (
          <Box className="flex flex-col md:flex-row items-start gap-4 mb-6">
            <Typography
              component="label"
              className="text-white font-medium w-full md:w-[150px] pt-2"
            >
              Media
            </Typography>
            <Box className="w-full" sx={{ bgcolor: "#2A2A2A", p: 3, borderRadius: "4px" }}>
              <Box className="!flex !gap-x-3">
                <PhotoIcon className="!text-2xl" />
                <Typography className="!text-white !text-lg">Photos</Typography>
              </Box>

              <Box className="!w-full">
                <Controller
                  name="eventImageUrls"
                  control={control}
                  render={() => {
                    const isEmpty = actPhotos?.length === 0;
                    return (
                      <Box
                        {...getRootProps({
                          className: "cursor-pointer",
                        })}
                        className={classNames("!mt-3 !w-full !rounded-[2px]", {
                          "!border-dashed !h-[220px] !flex !justify-center !items-center !border-[2px] !border-white":
                            isEmpty,
                        })}
                      >
                        <Box className="w-full">
                          <input {...getInputProps()} />
                          {isEmpty ? (
                            <Box className="!h-full !flex !flex-col !items-center !justify-center">
                              <Box className="!flex !gap-x-4 !items-center">
                                <DragAndDropIcon className="!text-base" />
                                <Typography className="!text-white !text-sm">
                                  Drag & Drop
                                </Typography>
                              </Box>
                              <Typography className="!text-center !text-white !text-sm !py-3">
                                Drag and drop your files here or click to browse
                              </Typography>
                            </Box>
                          ) : (
                            <Box className="!flex !flex-wrap !gap-3 relative">
                              {actPhotos?.length > 0 &&
                                actPhotos?.map((file, index) => (
                                  <Box key={index} className="relative">
                                    <CommonImage
                                      src={file}
                                      alt={"Preview media"}
                                      className={cn(
                                        "!border !relative !w-[131px] !h-[131px] !rounded-[4px] !border-[--divider-color]",
                                      )}
                                      width={1}
                                      height={1}
                                      layout="responsive"
                                    />
                                    {index === 0 && (
                                      <Typography className="!text-white !text-xs !absolute !left-0 !px-1 !py-2 !top-2 !bg-[#4caf50] !rounded-e-[8px]">
                                        Cover
                                      </Typography>
                                    )}
                                    <IconButton
                                      className="!p-1 !rounded-full !bg-white !absolute !right-2 !top-2"
                                      onClick={(event) => {
                                        handleDeleteImage(event, file);
                                      }}
                                    >
                                      <DeleteIcon className="!text-[21px] " />
                                    </IconButton>
                                  </Box>
                                ))}
                              <Box
                                className={cn(
                                  "!border-dashed !border-[2px] !rounded-[2px] !border-white !h-[131px]",
                                  "w-[131px]",
                                )}
                              >
                                <Box className="!h-full !flex !p-2 !flex-col !items-center !justify-center">
                                  <DragAndDropIcon className="!text-base" />
                                  <Typography className="!text-white !text-center !leading-[22.4px] !text-sm">
                                    Drag & Drop
                                  </Typography>
                                  <Typography className="!text-center !text-white !text-sm !pt-4">
                                    to upload
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    );
                  }}
                />
                <div className="mt-4">
                  <NoteList notes={imageNotes} />
                  {errors && errors.actPhotos && (
                    <span className="!mt-1 text-sm !text-red-600">{errors.actPhotos.message}</span>
                  )}
                </div>

                {errors && errors.eventImageUrls && (
                  <span className="!mt-1 text-sm !text-red-600">
                    {errors.eventImageUrls.message}
                  </span>
                )}
              </Box>
            </Box>
          </Box>
        )}

        <input type="hidden" {...register("ownerProfileId")} value={profileId || ""} />

        <Box className="flex justify-end">
          <Button
            className="!bg-[--text-color] text-black font-bold text-[16px] !gap-x-2 !py-2 !px-4 !rounded-md"
            sx={{
              minWidth: 0,
              opacity: (!isDirty && eventId) || isSubmitting ? 0.5 : 1,
            }}
            type="submit"
            disabled={(!isDirty && eventId) || isSubmitting}
          >
            {getButtonText()}
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default EventForm;
