import Avatar from "@/assets/png/Avatar.png";
import Image from "@/assets/png/AvatarImage.png";
import ConfirmedTag from "@/common/(tags)/tags-confirmed/tags-confirmed.common";
import DeclinedTag from "@/common/(tags)/tags-declined/tags-declined.common";
import DoneTag from "@/common/(tags)/tags-done/tags-done.common";
import DraftTag from "@/common/(tags)/tags-draft/tags-draft.common";
import MessageTag from "@/common/(tags)/tags-message/tags-message.common";
import NegotiatingTag from "@/common/(tags)/tags-negotiating/tags-negotiating.common";
import SentTag from "@/common/(tags)/tags-sent/tag-sent.common";
import Tag from "@/common/(tags)/tags-tag/tags-tag.common";

export const contractByMeData = [
  {
    id: 0,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <Tag />,
  },
  {
    id: 1,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    contractWork: "Continue working",
    tags: <DraftTag />,
  },
  {
    id: 2,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <SentTag />,
    contractTag: <Tag />,
  },
  {
    id: 3,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Name",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    contractWork: "Leave a feedback",
    tags: <DoneTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 4,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer_name",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <SentTag />,
    contractTag: <Tag />,
  },
  {
    id: 5,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <ConfirmedTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 6,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <DeclinedTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 7,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 8,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 9,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 10,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <MessageTag />,
  },
  {
    id: 11,
    venueImage: Avatar,
    venueName: "Venue",
    performerImage: Image,
    performerName: "Performer",
    performanceDate: "Oct 18-Dec 20, 2023",
    preformancePrice: "3k CAN",
    tags: <NegotiatingTag />,
    contractTag: <MessageTag />,
  },
];
