"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import { Button, Dropdown } from "@/component";
import { Add, Clear, SouthEast } from "@mui/icons-material";
import { Box, IconButton, InputAdornment, TextField, Typography } from "@mui/material";
import React, { useState, useEffect, useRef } from "react";
import OutlinedSearch from "@/assets/svg/OutlinedSearch.svg";
import Link from "next/link";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import ProfileCard from "@/common/profile-card/profile-card.common";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";
// import MobileViewFilter from "@/common/mobile-view-filter/mobile-view-filter.common";
import { useSelector, useDispatch } from "react-redux";
import { Loader } from "@/component";
import { getProfiles } from "@/store/slice/act/act.slice";
import { stringifyParams } from "@/utils";
import { useDebounce } from "use-debounce";
import { getSupportedProfiles } from "@/store/slice/common/common.slice";
import { useLocale, useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";
// import ProfileDrawer from "@/common/profile-drawer/profile-drawer.common";
import DrawerUi from "@/ui/drawer/drawer-ui";
import NotificationPopup from "@/common/notifications-popup/notifications-popup.common";
import Notification from "@/assets/svg/Notification.svg";
import usePaginate from "@/hooks/usePaginate";
import { appendQueryParams } from "@/utils/queryparams";
import { useRouter, useSearchParams } from "next/navigation";
import Filter from "@/assets/svg/Filter.svg";

const Profiles = () => {
  const t = useTranslations("profiles");
  const s = useTranslations("dropdown");
  const lang = useLocale();
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { profiles: dashBoardProfiles, loading } = useSelector((state) => state.act);

  const { supportedProfiles } = useSelector((state) => state.common);
  const { pageNo, search, onSearch } = usePaginate();
  const profiles = supportedProfiles;
  //const [search, setSearch] = useState("");
  const [open, setOpen] = useState(false);
  const selectedProfile = searchParams.get("selectProfile") || "";
  //const [selectedProfile, setSelectedProfile] = useState("");
  const [openPopup, setOpenPopup] = useState(false);
  const [keyword, setKeyword] = useState("");
  const startSearchNow = useRef(false);
  const [debounceSearch] = useDebounce(keyword, 1000);
  const { token } = useSelector((state) => state.login);

  const handleClosePopup = () => {
    setOpenPopup(false);
  };
  const handleOpenPopup = () => {
    setOpenPopup(true);
  };
  const setSelectedProfile = (value) => {
    appendQueryParams({ selectProfile: value }, router, searchParams);
  };
  const toggleSortDrawer = (newOpen) => {
    setOpen(newOpen);
  };

  const handleSelectProfile = (value) => {
    if (value === "Select Profile") {
      value = "";
    }
    if (value === "Venue") {
      value = "VENUE_PROFILE";
    }
    if (value === "Act") {
      value = "ACT_PROFILE";
    }
    setSelectedProfile(value);
  };

  {
    /** page and size  => pagination */
  }
  const page = pageNo - 1 || 0;
  const size = 12;

  useEffect(() => {
    if (startSearchNow.current) {
      onSearch(debounceSearch);
      startSearchNow.current = false;
    }
  }, [debounceSearch, onSearch]);

  const params = stringifyParams({
    page,
    size,
    profileType: selectedProfile,
    searchString: search,
  });
  useEffect(() => {
    dispatch(getProfiles(params));
  }, [selectedProfile, search, page]);

  useEffect(() => {
    dispatch(getSupportedProfiles());
  }, []);
  const { instantMessage } = useSelector((state) => state.instantMessage);
  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !pt-5 !w-full">
          <Box className="!flex !px-4 items-center lg:justify-end justify-between">
            <Box className="ml-4 mb-2 lg:hidden inline">
              <Link href={token ? `/${lang}/search` : `/${lang}`}>
                {/* <Logo className="!w-[70px] !h-6" /> */}
                <LogoComponent />
              </Link>
            </Box>
            <Box className="lg:!hidden !flex !items-center !gap-x-3">
              <Link href={`/${lang}/create-profiles`}>
                <Button className="!flex !items-center !gap-x-2 !py-2" sx={{ minWidth: 0 }}>
                  <Typography className="!normal-case !underline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--text-color]">
                    {t("newProfile")}
                  </Typography>
                  <SouthEast className=" !text-[--text-color] !text-2xl" />
                </Button>
              </Link>
              <IconButton sx={{ padding: 0 }} onClick={handleOpenPopup} className="relative">
                <Notification className="!w-6 !h-6 !cursor-pointer" />
                {instantMessage.length > 0 && (
                  <Typography className="text-[12px] text-[--bg-color] font-craftWorkHeavy bg-[--text-color] px-1 rounded-full absolute top-[-8px] right-[-5px]">
                    {instantMessage.length > 0 &&
                      instantMessage.filter(
                        (item) => item?.dismissed === false && item?.seen === false,
                      ).length}
                  </Typography>
                )}
              </IconButton>
              <Link href={`/${lang}/user/account-information`}>
                <OutlinedUser className="!w-6 !h-6 !cursor-pointer" />{" "}
              </Link>
            </Box>
            <Box className="!hidden lg:!flex !gap-x-3">
              <Dropdown
                options={["Select Profile", ...profiles]}
                onSelect={handleSelectProfile}
                selectedValue={
                  selectedProfile === "ACT_PROFILE"
                    ? "Act"
                    : selectedProfile === "VENUE_PROFILE"
                      ? "Venue"
                      : selectedProfile
                }
                title={s("selectProfile")}
                className="!text-[--text-color]"
              />
              <TextField
                size="small"
                placeholder={t("search")}
                className="Sora500 !text-[--text-color] !border !border-[--text-color] !rounded-[4px]"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end" style={{ cursor: "pointer" }}>
                      {search && (
                        <Clear
                          className="!text-[--text-color] !text-base"
                          onClick={() => {
                            startSearchNow.current = true;
                            setKeyword("");
                          }}
                        />
                      )}
                    </InputAdornment>
                  ),
                  startAdornment: (
                    <InputAdornment position="start" style={{ cursor: "pointer" }}>
                      <OutlinedSearch className="!text-2xl" />
                    </InputAdornment>
                  ),
                }}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                value={keyword || search}
                onChange={(event) => {
                  startSearchNow.current = true;
                  setKeyword(event.target.value);
                }}
              />
              <Link href={`/${lang}/create-profiles`}>
                <Button className="!bg-[--text-color] !gap-x-4 !py-2" sx={{ minWidth: 0 }}>
                  <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                    {t("newProfile")}
                  </Typography>
                  <Add className="!text-[--bg-color] !text-2xl" />
                </Button>
              </Link>
            </Box>
          </Box>
          <Box className="flex lg:hidden gap-4 justify-between px-6">
            <TextField
              size="small"
              placeholder={t("search")}
              className="Sora500 !text-[--text-color] !border !border-[--text-color] !rounded-[4px]"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <Clear
                        className="!text-[--text-color] !text-base"
                        onClick={() => {
                          startSearchNow.current = true;
                          setKeyword("");
                        }}
                      />
                    )}
                  </InputAdornment>
                ),
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer" }}>
                    <OutlinedSearch className="!text-2xl" />
                  </InputAdornment>
                ),
              }}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              value={keyword || search}
              onChange={(event) => {
                startSearchNow.current = true;
                setKeyword(event.target.value);
              }}
            />
            <IconButton onClick={() => toggleSortDrawer(true)} className="lg:hidden">
              <Filter className="!text-2xl" />
            </IconButton>
          </Box>
          <NotificationPopup open={openPopup} handleClose={handleClosePopup} />
          {/* <Box className="!px-4">
            <MobileViewFilter
              value={keyword || search}
              onChange={(event) => {
                startSearchNow.current = true;
                setKeyword(event.target.value);
              }}
              placeholder="Search your profiles"
              handleOpen={() => toggleSortDrawer(true)}
            />
          </Box> */}
          <Typography className="!text-[--text-color] pl-4 pt-4 !text-2xl CraftworkGroteskMedium">
            {t("profiles")}
          </Typography>
          <Box className="!mt-4 !pb-20 lg:!pb-0 !px-4 lg:!px-0">
            <ProfileCard profiles={dashBoardProfiles} type="profile" params={params} />
          </Box>
        </Box>
      </Box>
      {/* <ProfileDrawer
        open={open}
        handleClose={() => toggleSortDrawer(false)}
        setSelectedProfile={setSelectedProfile}
        selectedProfile={selectedProfile}
      /> */}
      <DrawerUi
        open={open}
        handleClose={() => toggleSortDrawer(false)}
        setSelectedProfile={setSelectedProfile}
        selectedProfile={selectedProfile}
        type="Profile"
      />
    </>
  );
};

export default Profiles;
