import FavouritesEventCard from "@/common/favourites-card/favourites-event-card.common";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import ScreenViewTitle from "@/common/title/screen-view.title.common";
import { Box, Typography } from "@mui/material";
import React from "react";
import { favouritesEventData } from "./data";
import Link from "next/link";
import { West } from "@mui/icons-material";
import { Button } from "@/component";
import LikeIcon from "@/assets/svg/LikeIcon.svg";
import { useLocale, useTranslations } from "next-intl";

const FavouritesEvent = () => {
  const t = useTranslations("favourites");
  const lang = useLocale();

  const links = [
    { id: 0, path: `/${lang}/favourites/acts`, text: t("acts") },
    {
      id: 1,
      path: `/${lang}/favourites/venues`,
      text: t("venues"),
    },
    {
      id: 4,
      path: `/${lang}/favourites/saved-searches`,
      text: t("saveSearchs"),
    },
  ];

  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className=" !block lg:!hidden !px-4 !pt-5">
        <Link href={`/${lang}/dashboard`}>
          <Button className="!flex !gap-2 !cursor-pointer !normal-case !pb-8">
            <West className="!text-2xl !text-[--text-color]" />
            <Typography className="!text-sm !text-[--text-color] font-craftWorkHeavy">
              Back
            </Typography>
          </Button>
        </Link>
        <LikeIcon className="!text-2xl" />
        <Typography className="!text-[--text-color] !text-lg font-craftWorkMedium !pt-2">
          {t("favourites")}
        </Typography>
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !pt-0 !w-full">
          <ScreenViewTitle primaryText={t("favourites")} links={links} />
          <Box className="!flex !flex-wrap !gap-6 !pb-5 !mt-4 !px-4 lg:!px-0">
            <FavouritesEventCard favouritesData={favouritesEventData} />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default FavouritesEvent;
