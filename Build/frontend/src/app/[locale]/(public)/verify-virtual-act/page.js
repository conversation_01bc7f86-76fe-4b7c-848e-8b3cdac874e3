"use client";
import { Button, CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import EmailVerificationPng from "@/assets/png/EmailVerification.png";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import { useSearchParams } from "next/navigation";
import CancelIcon from "@mui/icons-material/Cancel";
import Link from "next/link";
import { verifyVirtualAct } from "@/store/slice/common/common.slice";
import { useDispatch } from "react-redux";
import { Loader } from "@/component";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";

const EmailVerificationSuccess = ({ params }) => {
  const t = useTranslations("verifyVirtualAct");
  const s = useTranslations("signUp");
  const searchParms = useSearchParams();
  const token = searchParms.get("token") ?? "";
  const dispatch = useDispatch();
  const [status, setStatus] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    dispatch(verifyVirtualAct(token))
      .unwrap()
      .then((data) => {
        setLoading(false);
        if (data?.status === "success") {
          setStatus("success");
        } else {
          setStatus("failed");
        }
      })
      .catch(() => {
        setLoading(false);
        setStatus("failed");
      });
  }, [token]);

  if (loading) return <Loader />;
  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="!hidden lg:!block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={EmailVerificationPng}
          alt="email-verification"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className="lg:basis-1/2 w-full md:px-8 px-4 relative flex-grow flex flex-col justify-between">
        <Box className="lg:top-6 lg:left-12 !py-6 flex lg:justify-normal justify-center">
          <Link href={`/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="max-w-[380px] !m-auto !mt-32 flex-grow">
          <Box className="!bg-[--footer-bg] !border !flex !flex-col !items-center !p-6 !border-[--divider-color] !shadow-lg !rounded-[4px]">
            {status === "success" ? (
              <CheckCircleOutlinedIcon className="!min-w-16 !text-[#28a745] !min-h-16" />
            ) : (
              <CancelIcon className="!min-w-16 !text-red-500 !min-h-16" />
            )}
            <Typography className="text-[--text-color] !my-8 CraftworkGroteskMedium !text-xl !text-center">
              {status === "success" ? t("successfully") : t("failed")}
            </Typography>

            <Link href={`/${params.locale}/login`} className="!w-full">
              <Button
                className="!bg-[--text-color] !w-full !mt-6 !py-4"
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                  {s("logIn")}
                </Typography>
              </Button>
            </Link>
          </Box>
        </Box>
        <DesktopFooter className="mx-auto !my-8" />
      </Box>
    </Box>
  );
};

export default EmailVerificationSuccess;
