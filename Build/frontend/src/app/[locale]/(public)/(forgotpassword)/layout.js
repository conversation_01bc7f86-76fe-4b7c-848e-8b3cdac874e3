"use client";
//import { useRouter } from "next/navigation";
//import { useEffect } from "react";
//import { getLocalStorage } from "@/utils";

const Layout = ({ children }) => {
  // const router = useRouter();
  // useEffect(() => {
  //   if (getLocalStorage("forgotPasswordEmail")===null) {
  //     router.push("/password-recovery");
  //   }
  // }, []);

  return <>{children}</>;
};

export default Layout;
