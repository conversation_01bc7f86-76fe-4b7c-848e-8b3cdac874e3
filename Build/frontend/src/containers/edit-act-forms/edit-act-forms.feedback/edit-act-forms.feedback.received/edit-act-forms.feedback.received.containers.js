import { Button, CommonImage } from "@/component";
import Rating from "@/component/rating/rating.components";
import EditActFeedbackRate from "@/ui/edit-act-feedback-rate/edit-act-feedback-rate.ui";
import { Box, Typography } from "@mui/material";
import React from "react";
import DistributionImage from "@/assets/png/DistributionImage.png";
import Link from "next/link";
import { SouthEast } from "@mui/icons-material";
import { useLocale, useTranslations } from "next-intl";
import PublicSvg from "@/assets/svg/PublicSvg.svg";
import PrivateSvg from "@/assets/svg/PrivateSvg.svg";

const EditActFeedbackReceived = ({ feedbackData, feedbackType }) => {
  const t = useTranslations("feedback");
  const s = useTranslations("actReview.actReviewLocation");
  const lang = useLocale();
  const rateData = [
    {
      id: 0,
      text: t("entertainment"),
      rating: feedbackData?.actRating?.entertainmentValueRating,
    },
    {
      id: 1,
      text: t("professionalism"),
      rating: feedbackData?.actRating?.professionalismRating,
    },
    {
      id: 2,
      text: t("draw"),
      rating: feedbackData?.actRating?.drawRating,
    },
  ];

  return (
    <Box className="!pt-8">
      {feedbackType === "Received" && (
        <Box className="!flex !justify-between !items-center">
          <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium">
            {t("rate")}
          </Typography>
          <Box className="!flex !flex-wrap !gap-1 !items-center !mt-4">
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {feedbackData?.actRating?.overallRating}
            </Typography>
            <Rating value={feedbackData?.actRating?.overallRating} readOnly />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              ({feedbackData?.actRating?.numberOfRatings} {s("reviews")})
            </Typography>
          </Box>
        </Box>
      )}
      {feedbackType === "Received" && <EditActFeedbackRate rateData={rateData} />}
      {feedbackType === "Received" &&
        feedbackData?.receivedFeedbacks?.map((feedback, index) => (
          <Box
            className="!border !border-[--divider-color] !bg-[--footer-bg] !rounded-[4px] !w-full !p-4"
            key={index}
          >
            <Box className="!flex !justify-between !items-center pb-2">
              <Box className="!flex !gap-x-2">
                <CommonImage
                  src={feedback?.providerImageUrls?.[0] ?? DistributionImage}
                  alt="image"
                  width={38}
                  height={38}
                  className="!rounded-full !w-[38px] !h-[38px]"
                />
                <Box>
                  <Typography className="!text-sm !text-[--text-color] CraftworkGroteskregular">
                    {feedback?.providerName} nn
                  </Typography>
                  {/* <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskregular">
                  {t("booking")}
                </Typography> */}
                </Box>
              </Box>
              <Box className="!flex !gap-2">
                {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !bg-[--inprogress-color] !text-center !py-1 !px-[6px] !rounded-[2px]">
                {t("new")}
              </Typography> */}
                <Link href={`/${lang}/booking-details?contract-id=${feedback?.contractId}`}>
                  <Button
                    className=" flex gap-x-2"
                    sx={{
                      border: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                  >
                    <Typography className="!normal-case CraftworkGroteskHeavy !underline !text-sm !leading-[15.4px] !text-[--text-color]">
                      {t("viewContract")}
                    </Typography>
                    <SouthEast className="!text-xl" />
                  </Button>
                </Link>
              </Box>
            </Box>
            <EditActFeedbackRate
              rateData={[
                {
                  id: 0,
                  text: t("entertainment"),
                  rating: feedback?.entertainmentValue,
                },
                {
                  id: 1,
                  text: t("professionalism"),
                  rating: feedback?.professionalismValue,
                },
                {
                  id: 2,
                  text: t("draw"),
                  rating: feedback?.drawAsExpectedValue,
                },
              ]}
            />
            <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-2">
              <Box className="flex gap-2 items-center">
                <PublicSvg />
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Public
                </Typography>
              </Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
                {feedback?.publicMessage}
              </Typography>
              {/* <Box className="flex items-center !mt-2">
              <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
              <label className="cursor-pointer flex gap-x-2 items-center">
                <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                  Add to my public account
                </Typography>
              </label>
            </Box> */}
            </Box>
            <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-4">
              <Box className="flex gap-2 items-center">
                <PrivateSvg />
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Private
                </Typography>
              </Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
                {feedback?.privateMessage}
              </Typography>
              {/* <Box className="flex items-center !mt-2">
              <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
              <label className="cursor-pointer flex gap-x-2 items-center">
                <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                  Add to my public account
                </Typography>
              </label>
            </Box> */}
            </Box>
          </Box>
        ))}

      {feedbackType === "Initiated" &&
        feedbackData?.providedFeedbacks?.map((feedback, index) => (
          <Box
            className="!border !border-[--divider-color] !bg-[--footer-bg] !rounded-[4px] !w-full !p-4"
            key={index}
          >
            <Box className="!flex !justify-between !items-center pb-2">
              <Box className="!flex !gap-x-2">
                <CommonImage
                  src={feedback?.providerImageUrls?.[0] ?? DistributionImage}
                  alt="image"
                  className="!rounded-full !w-[38px] !h-[38px]"
                />
                <Box>
                  <Typography className="!text-sm !text-[--text-color] CraftworkGroteskregular">
                    {feedback?.otherPartyName}
                  </Typography>
                  {/* <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskregular">
                  {t("booking")}
                </Typography> */}
                </Box>
              </Box>
              <Box className="!flex !gap-2">
                {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !bg-[--inprogress-color] !text-center !py-1 !px-[6px] !rounded-[2px]">
                {t("new")}
              </Typography> */}
                <Link href={`/${lang}/booking-details?contract-id=${feedback?.contractId}`}>
                  <Button
                    className=" flex gap-x-2"
                    sx={{
                      border: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                  >
                    <Typography className="!normal-case CraftworkGroteskHeavy !underline !text-sm !leading-[15.4px] !text-[--text-color]">
                      {t("viewContract")}
                    </Typography>
                    <SouthEast className="!text-xl" />
                  </Button>
                </Link>
              </Box>
            </Box>
            <EditActFeedbackRate
              rateData={[
                {
                  id: 0,
                  text: t("entertainment"),
                  rating: feedback?.entertainmentValue,
                },
                {
                  id: 1,
                  text: t("professionalism"),
                  rating: feedback?.professionalismValue,
                },
                {
                  id: 2,
                  text: t("draw"),
                  rating: feedback?.drawAsExpectedValue,
                },
              ]}
            />
            <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-2">
              <Box className="flex gap-2 items-center">
                <PublicSvg />
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Public
                </Typography>
              </Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
                {feedback?.publicMessage}
              </Typography>
              {/* <Box className="flex items-center !mt-2">
              <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
              <label className="cursor-pointer flex gap-x-2 items-center">
                <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                  Add to my public account
                </Typography>
              </label>
            </Box> */}
            </Box>
            <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-4">
              <Box className="flex gap-2 items-center">
                <PrivateSvg />
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Private
                </Typography>
              </Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
                {feedback?.privateMessage}
              </Typography>
              {/* <Box className="flex items-center !mt-2">
              <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
              <label className="cursor-pointer flex gap-x-2 items-center">
                <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                  Add to my public account
                </Typography>
              </label>
            </Box> */}
            </Box>
          </Box>
        ))}
    </Box>
  );
};

export default EditActFeedbackReceived;
