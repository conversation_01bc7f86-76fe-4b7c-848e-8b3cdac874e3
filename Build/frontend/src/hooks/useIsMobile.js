"use client";
// import { useEffect, useState } from 'react';

// const useIsMobile = () => {
//   const [isMobile, setIsMobile] = useState(false);

//   useEffect(() => {
//     const checkIsMobile = () => {
//       const userAgent = typeof window.navigator === 'undefined' ? '' : window.navigator.userAgent;
//       const mobile = /iPhone|iPad|iPod|Android/i.test(userAgent);
//       setIsMobile(mobile);
//     };

//     checkIsMobile();

//     window.addEventListener('resize', checkIsMobile);
//     return () => window.removeEventListener('resize', checkIsMobile);
//   }, []);

//   return isMobile;
// };

// export default useIsMobile;

import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

/**
 * This is a simple utility function that checks if the window's inner width is less than 600px.
 *
 * @returns {boolean} - Returns true if the window's inner width is less than 600px, otherwise false.
 */
export function isMobile() {
  return window.innerWidth < 1024;
}

/**
 * Custom hook to determine if the viewport matches the "xs" breakpoint defined in the MUI theme.
 *
 * This considers `xs` as mobile. For more specific use cases, you can utilize the example provided.
 *
 * @example
 * import { useTheme } from '@mui/material/styles';
 * import useMediaQuery from '@mui/material/useMediaQuery';
 *
 * function MyComponent() {
 *   const theme = useTheme();
 *   const matches = useMediaQuery(theme.breakpoints.up('sm'));
 *
 *   return <span>{`theme.breakpoints.up('sm') matches: ${matches}`}</span>;
 * }
 *
 * @returns {boolean} - Returns true if the viewport matches the "xs" breakpoint, otherwise false.
 */
export default function useIsMobile() {
  const theme = useTheme();

  return useMediaQuery(theme.breakpoints.down("sm"));
}
