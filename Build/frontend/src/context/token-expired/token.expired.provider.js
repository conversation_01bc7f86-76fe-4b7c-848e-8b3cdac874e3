"use client";
import { useState } from "react";
import TokenExpiredContext from "./token.expired.context";
import TokenExpirePopup from "@/ui/token-expire-popup/token-expire-popup.ui";

const TokenExpiredProvider = ({ children }) => {
  const [isTokenExpired, setIsTokenExpired] = useState(false);
  const closeTokenExpiredPopup = () => {
    setIsTokenExpired(false);
  };
  return (
    <TokenExpiredContext.Provider
      value={{
        isTokenExpired,
        setIsTokenExpired,
        closeTokenExpiredPopup,
      }}
    >
      {children}
      <TokenExpirePopup />
    </TokenExpiredContext.Provider>
  );
};

export default TokenExpiredProvider;
