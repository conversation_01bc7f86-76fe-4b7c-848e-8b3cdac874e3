.gm-style .gm-style-iw-c {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.3);
}

.gm-style .gm-style-iw-d {
  overflow: hidden !important;
  padding: 0 !important;
}

.gm-style .gm-style-iw-t::after {
  background: linear-gradient(45deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 51%, rgba(255, 255, 255, 0) 100%);
  box-shadow: -2px 2px 2px 0 rgba(178, 178, 178, .4);
}

.info-window-content {
  padding: 1rem;
  max-width: 300px;
}

.info-window-content img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  margin: -1rem -1rem 1rem -1rem;
  max-width: calc(100% + 2rem);
}

.info-window-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
}

.info-window-content p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}
