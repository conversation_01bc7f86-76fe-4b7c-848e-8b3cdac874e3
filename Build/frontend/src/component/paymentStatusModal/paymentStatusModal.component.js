"use client";

import { useEffect, useState } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { Typography } from "@mui/material";
import Button from "../button/button.component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Clear } from "@mui/icons-material";
import {
  verifyPaymentStatusAction,
  initializeStripePayment,
} from "@/store/slice/stripe/stripe.slice";
import { useDispatch } from "react-redux";
import PaymentModalBlue from "@/assets/svg/PaymentModalBlue.svg";
import PaymentModalRed from "@/assets/svg/PaymentModalRed.svg";
import { showSnackbar } from "@/utils/snackbar.utils";
import { previewContract } from "@/store/slice/booking/booking.slice";

const styles = {
  modalOverlay: {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: "#1e1e1e",
    borderRadius: "8px",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    width: "100%",
    maxWidth: "550px",
    padding: "24px",
    color: "white",
  },
  modalContent: {
    display: "flex",
    flexDirection: "column",
    alignItems: "start",
  },
  iconContainer: {
    marginBottom: "16px",
  },
  documentIcon: {
    width: "48px",
    height: "48px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  errorIcon: {
    // Add any specific styles for the error icon if needed
  },
  title: {
    fontSize: "39px",
    fontWeight: 600,
    marginBottom: "8px",
    textAlign: "center",
  },
  message: {
    fontSize: "16px",
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: "16px",
    textAlign: "start",
    lineHeight: 1.5,
    width: "100%",
  },
  ul: {
    paddingLeft: "18px",
    fontSize: "12px",
    listStyleType: "disc",
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: "24px",
    textAlign: "start",
    lineHeight: 1.5,
    width: "100%",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "center",
    gap: "12px",
    width: "100%",
  },
  cancelButtonContainer: {
    display: "flex",
    justifyContent: "center",
    gap: "8px",
    width: "30%",
  },
};

const PaymentStatusModal = () => {
  const [open, setOpen] = useState(false);
  const [status, setStatus] = useState(null);
  const [contractId, setContractId] = useState(null);
  const [verified, setVerified] = useState(false); // Tracks verification status
  const [loading, setLoading] = useState(true); // Tracks API call progress

  const searchParams = useSearchParams();
  const router = useRouter();
  const dispatch = useDispatch();

  // Get query parameters and open modal if they exist
  useEffect(() => {
    const statusParam = searchParams.get("status");
    const contractIdParam = searchParams.get("contract-id");

    if (statusParam && contractIdParam) {
      setStatus(statusParam);
      setContractId(contractIdParam);
      setOpen(true);

      const sessionId = localStorage.getItem("sessionId");

      // Verify payment status if status is "success"
      if (statusParam === "success") {
        setLoading(true); // Start loading
        dispatch(verifyPaymentStatusAction(sessionId))
          .unwrap()
          .then((response) => {
            if (response?.status === "success") {
              setVerified(true); // Mark as verified if the response indicates success
            } else {
              setVerified(false); // Mark as not verified if the response indicates failure
            }
          })
          .catch((error) => {
            showSnackbar(error, "Error verifying payment status");
            setVerified(false); // Mark as not verified on error
          })
          .finally(() => {
            setLoading(false); // Stop loading after API call
          });
      } else {
        setLoading(false); // No need to verify if status is not "success"
      }
    }
  }, [searchParams, dispatch]);

  const reInitializePayment = () => {
    dispatch(initializeStripePayment(contractId))
      .unwrap()
      .then((response) => {
        localStorage.setItem("sessionId", response.data?.sessionId);
        if (response.data?.sessionId && response.data?.sessionUrl) {
          router.push(response.data?.sessionUrl);
        }
      })
      .catch((error) => {
        showSnackbar(error, "Error reinitializing payment status");
      });
  };

  const handleClose = () => {
    setOpen(false);
  };

  if (!open || loading) {
    // Show nothing or a loading spinner while waiting for the API response
    return null;
  }

  const showDetails = () => {
    dispatch(previewContract(contractId));
    handleClose();
    router.replace(`/en/booking-details?contract-id=${contractId}`);
  };
  return (
    <div style={styles.modalOverlay}>
      <div style={styles.modalContainer}>
        {status === "success" && verified ? (
          <div style={styles.modalContent}>
            <div style={styles.iconContainer}>
              <div style={styles.documentIcon}>
                <PaymentModalBlue width={40} height={40} />
              </div>
            </div>
            <h2 style={styles.title}>Payment has been accepted</h2>
            <p style={styles.message}>Thank you for choosing our platform!</p>
            <div style={styles.buttonContainer}>
              <Button
                className="!bg-[--text-color] !w-full px-3 py-[20px] !gap-x-2"
                sx={{
                  minWidth: 0,
                  height: 20,
                  padding: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
                onClick={showDetails}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                  Ok
                </Typography>
                <ArrowSouthEast alt="arrow" />
              </Button>
            </div>
          </div>
        ) : (
          <div style={styles.modalContent}>
            <div style={styles.iconContainer}>
              <div style={{ ...styles.documentIcon, ...styles.errorIcon }}>
                <PaymentModalRed width={40} height={40} />
              </div>
            </div>
            <h2 style={styles.title}>Payment unsuccessful</h2>
            <p style={styles.message}>
              Oops! It seems there was an issue processing your payment for the service fee. Please
              ensure that the provided payment details are correct, and try again. If the problem
              persists, consider checking with your card provider or using an alternative payment
              method.
            </p>
            <ul style={styles.ul}>
              <li>
                Your booking will only be confirmed upon successful payment of the service fee.
              </li>
              <li>
                For any questions or support, feel free to reach out to our support team at{" "}
                <a href="mailto:<EMAIL>"><EMAIL></a>.
              </li>
            </ul>
            <div style={styles.buttonContainer}>
              <Button
                style={styles.cancelButtonContainer}
                type="button"
                sx={{ border: 0 }}
                className="flex items-center !normal-case"
                onClick={showDetails}
              >
                <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
                  Try later
                </Typography>
                <Clear className="text-lg text-[--text-color]" />
              </Button>
              <Button
                className="!bg-[--text-color] !w-full px-3 py-[20px] !gap-x-2"
                sx={{
                  minWidth: 0,
                  height: 20,
                  padding: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
                onClick={reInitializePayment}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                  Try again
                </Typography>
                <ArrowSouthEast alt="arrow" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusModal;
