"use client";

import { useState, useRef, useEffect } from "react";
import dayjs from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import FullCalendar from "@fullcalendar/react";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import styled from "@emotion/styled";

import {
  BUTTON_TEXT,
  COLORS_SWATCH,
  HEADER_TOOLBAR,
  INITIAL_VIEW,
  PLUGINS,
  defaultIntervalInMinutes,
  filterEventData,
} from "@/utils";
import useImageButton from "./hooks/useImageButton";
import useIconButton from "./hooks/useIconButton";
//import CreateEvent from "./CreateEvent";
//import { createId } from "./helper";
import renderEventContent from "./methods/renderEventContent";
//import { useEventContext } from "@/context";
import EditEvent from "./EditEvent";
import Header from "./methods/Header";
import { getSchedules } from "../../store/slice/act/act.slice";
import { useDispatch } from "react-redux";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useForm } from "react-hook-form";
import { scheduleValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";

export const CalendarWarpper = styled.div`
  .fc .fc-col-header-cell-cushion,
  .fc td,
  .fc.th,
  .fc .fc-toolbar-title {
    color: var(--text-color);
    font-family: var(--craftworkMedium);
  }
  .fc .fc-button-primary {
    background-color: transparent;
    border: 0px;
  }

  .fc-view .fc-scrollgrid {
    border-radius: 8px;
    background-color: var(--footer-bg);
  }
  .fc-scrollgrid-section .fc-timegrid-divider {
    display: none;
  }

  .fc-theme-standard .fc-scrollgrid {
    border: 1px solid var(--divider-color);
  }

  .fc-timegrid-divider {
    display: hidden;
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    border: 1px solid var(--divider-color) !important;
  }
  @media (max-width: 600px) {
    .fc {
      height: 700px !important;
    }
  }
`;

function BookingCalender({
  selectable: select,
  profileId,
  editable = false,
  type = "UNAVAILABLE",
}) {
  const fullCalendarRef = useRef(null);
  //const [open, setOpen] = useState(false);
  const [openCreateEvent, setOpenCreateEvent] = useState(false);
  const [events, setEvents] = useState([]);
  const [fetch, setFetch] = useState(0);
  const [loading, setLoading] = useState(true);
  //eslint-disable-next-line
  const [selectable, setSelectable] = useState(select);
  const goToToday = () => fullCalendarRef.current?.calendar?.today?.();
  const gotToPrev = () => fullCalendarRef.current?.calendar?.prev?.();
  const gotToNext = () => fullCalendarRef.current?.calendar?.next?.();
  const handleViewChange = (view) => fullCalendarRef.current?.calendar?.changeView?.(view);
  const dispatch = useDispatch();
  const resolver = yupResolver(scheduleValidation);
  const [scheduleId, setScheduleId] = useState(null);
  //   const checkDays = (day) => {
  //     // check if day present in workingHoursList
  //     const daysMap = {
  //       MONDAY: "su",
  //       TUESDAY: "mo",
  //       WEDNESDAY: "tu",
  //       THURSDAY: "we",
  //       FRIDAY: "th",
  //       SATURDAY: "fr",
  //       SUNDAY: "sa",
  //     };
  //     const selectedDay = daysMap[day];
  //     // check if day present in workingHoursList and return true or false
  //     return selectedDay;
  //   };

  //   const getWeekOfMonth = (date) => {
  //     const day = date.getDate();
  //     const weekNumber = Math.ceil(day / 7) - 1;
  //     return weekNumber;
  //   };

  useEffect(() => {
    if (profileId) {
      dispatch(getSchedules(profileId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            const responseDate = response.data.data;
            const data = responseDate.map((item) => {
              return filterEventData(item, editable);
            });
            setLoading(false);
            setEvents(data);
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    } else {
      setLoading(false);
      setEvents([]);
    }
  }, [fetch]);

  // useEffect(() => {
  //   if (previewContract?.scheduleTime) {
  //     const dats = {
  //       ...previewContract?.scheduleTime,
  //       scheduleId: currentBookingStatus?.contractId,
  //     };
  //     const data = filterEventData(dats, true);
  //     setSelectable(false);
  //     //setEvents((prev) => [...prev, data]);
  //     setEvents(data);
  //   }
  // }, [previewContract?.scheduleTime]);

  // Previous Button
  const { prevBtn } = useIconButton({
    name: "prevBtn",
    icon: <ArrowBackIcon fontSize="large" sx={{ color: "black", margin: "2px" }} />,
    alt: "Previous",
    title: "Previous",
    onClick: gotToPrev,
  });

  // Next Button
  const { nextBtn } = useIconButton({
    name: "nextBtn",
    icon: (
      <ArrowBackIcon
        fontSize="large"
        sx={{
          color: "black",
          margin: "2px",
          rotate: "180deg",
          display: "inline-block",
        }}
      />
    ),
    alt: "Next",
    title: "Next",
    onClick: gotToNext,
  });

  // Today Button
  const { customToday } = useImageButton({
    name: "customToday",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom today",
    title: "Today",
    onClick: goToToday,
  });

  // Month Button
  const { customMonth } = useImageButton({
    name: "customMonth",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom month",
    title: "Month",
    onClick: () => handleViewChange("dayGridMonth"),
  });

  // Week Button
  const { customWeek } = useImageButton({
    name: "customWeek",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom week",
    title: "Week",
    onClick: () => handleViewChange("timeGridWeek"),
    imageStyles: { height: "28px", width: "28px", margin: "6px" },
  });

  // Day Button
  const { customDay } = useImageButton({
    name: "customDay",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom day",
    title: "Day",
    onClick: () => handleViewChange("timeGridDay"),
  });
  // use .svg files as image source
  // const { customDate } = useImageButton({
  //   name: "customDate",
  //   src: "/date.svg",
  //   alt: "Custom week",
  //   onClick: () => alert("Custom week"),
  // });

  // const { customIconButton } = useIconButton({
  //   name: "customIconButton",
  //   icon: <AddAlarm fontSize="large" />, // Use your icon component
  //   styles: { height: "40px", width: "40px" },
  //   onClick: () => alert("Custom icon button clicked"),
  // });

  const handleDateClick = (selectInfo) => {
    // let title = prompt("Please enter a new title for your event");

    // calendarApi.unselect(); // clear date selection
    const dates = [];
    if (selectInfo.dateStr) {
      dates.push(dayjs(selectInfo.dateStr));
    }

    if (selectInfo.endStr) {
      dates.push(dayjs(selectInfo.endStr));
    } else {
      dates.push(dayjs(selectInfo.dateStr).add(defaultIntervalInMinutes, "minutes"));
    }

    //setSelectedDates(dates);
    setValue("startDate", dates[0]);
    setValue("endDate", dates[1]);
    editable && setOpenCreateEvent(true);

    // if (title) {
    //   calendarApi.addEvent({
    //     id: createEventId(),
    //     title,
    //     start: selectInfo.startStr,
    //     end: selectInfo.endStr,
    //     allDay: selectInfo.allDay,
    //   });
    // }
  };

  const handleDateSelect = (selectInfo) => {
    // runs when dragging to select time
    const dates = [];
    if (selectInfo.startStr) {
      dates.push(dayjs(selectInfo.startStr));
    }

    if (selectInfo.endStr) {
      let endDateStr = dayjs(selectInfo.endStr);
      // in case of month view, subtract 1 minute from the end date, so that end date is not included
      if (selectInfo.view.type === "dayGridMonth") {
        endDateStr = endDateStr.subtract(1, "minute");
      }
      dates.push(endDateStr);
    } else {
      dates.push(dayjs(selectInfo.endStr).add(defaultIntervalInMinutes, "minutes"));
    }
    setValue("startDate", dates[0]);
    setValue("endDate", dates[1]);
    //setSelectedDates(dates);
    // calendarApi.unselect(); // clear date selection
    setOpenCreateEvent(true);

    // if (title) {
    //   calendarApi.addEvent({
    //     id: createEventId(),
    //     title,
    //     start: selectInfo.startStr,
    //     end: selectInfo.endStr,
    //     allDay: selectInfo.allDay,
    //   });
    // }
  };

  // const handleCreateEvent = ({ eventTitle, timezone, eventType, fromDate, toDate, eventColor }) => {
  //   // generate a unique id for the event
  //   const id = createId();
  //   fullCalendarRef.current?.calendar?.addEvent({
  //     id,
  //     title: eventTitle,
  //     start: fromDate.toDate(),
  //     end: toDate.toDate(),
  //     allDay: eventType === "recurring",
  //     color: eventColor,
  //     extendedProps: {
  //       // timezone,
  //       id,
  //       editable: false,
  //     },
  //   });
  //   setOpenCreateEvent(false);
  // };

  // const handleEditEvent = (data) => {
  //   // const id = createId();
  //   // fullCalendarRef.current?.calendar?.addEvent({
  //   //   id,
  //   //   title: eventTitle,
  //   //   start: fromDate.toDate(),
  //   //   end: toDate.toDate(),
  //   //   allDay: eventType === "recurring",
  //   //   color: eventColor,
  //   //   extendedProps: {
  //   //     // timezone,
  //   //     id,
  //   //     editable: editable,
  //   //   },
  //   // });
  //   setOpenCreateEvent(false);
  // };

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      profileType: "ACT",
      scheduleName: "",
      scheduleDescription: "",
      scheduleType: type,
      timeZone: "(GMT-04:00) Eastern Time - Toronto",
      startDate: new Date(),
      endDate: new Date(),
      recurring: false,
      recurrence: {
        recurrenceType: "WEEKLY",
        interval: 0,
        count: 0,
        endDate: "string",
        daysOfWeek: [],
        daysOfMonth: 0,
        monthsOfYear: ["JANUARY"],
        recurrenceEndType: {
          never: true,
          endDate: "2024-05-21T11:27:32.345Z",
          occurrences: 0,
        },
      },
    },
  });

  // function handleEvents(events) {
  //   if (events.length > 0) {
  //     //setLocalStorage("events", events);
  //   }
  //   setCurrentEvents(events);
  // }

  // const handleClickOpen = () => {
  //   setOpen(true);
  // };

  const handleClose = () => {
    setOpenCreateEvent(false);
    setScheduleId(null);
    // setOpen(false);
  };
  if (loading)
    return <div className="text-sm text-[--text-color] CraftworkGroteskGX">Loading...</div>;
  return (
    <CalendarWarpper>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Header ref={fullCalendarRef} />

        <FullCalendar
          ref={fullCalendarRef}
          plugins={PLUGINS}
          initialView={INITIAL_VIEW}
          weekends
          //here events data will come
          events={events}
          //events={filteredEvents.length > 0 ? filteredEvents : events}
          eventContent={renderEventContent}
          headerToolbar={HEADER_TOOLBAR}
          buttonText={BUTTON_TEXT}
          customButtons={{
            prevBtn,
            nextBtn,
            customToday,
            // customIconButton,
            customMonth,
            customWeek,
            customDay,
          }}
          // views={VIEWS}
          // When a date is clicked
          selectable={editable}
          // to disable the dragging after event is created
          eventStartEditable={false}
          // when selection is been made time appears
          selectMirror
          select={handleDateSelect}
          dateClick={handleDateClick}
          eventColor={COLORS_SWATCH[0].code}
          editable={editable}
          eventClick={(info) => {
            if (editable) {
              const event = events.find((event) => event.id === info.event._def.extendedProps.id);

              //setSelectedDates([dayjs(event.start), dayjs(event.end)]);
              setValue("startDate", dayjs(event.startDate));
              setValue("endDate", dayjs(event.endDate));
              setValue("recurring", event.recurring);
              setValue("timeZone", event.timeZone);
              event.recurring && setValue("recurrence.recurrenceType", event.recurrenceType);
              event.recurring && setValue("recurrence.daysOfWeek", event.daysOfWeek);
              event.recurring && setValue("recurrence.interval", event.interval);
              setOpenCreateEvent(true);
              setScheduleId(event.id);
            }
          }}
          // called after events are initialized/added/changed/removed
          //eventsSet={handleEvents}
          // validRange={{
          //   start: new Date(),
          // }}
        />

        {/* <CreateEvent
        key={openCreateEvent ? "open-create" : "close-create"}
        open={openCreateEvent}
        setOpen={setOpenCreateEvent}
        defaultValues={defaultAddEventValues}
        onCreateEvent={handleCreateEvent}
      />
      <EditEvent
        key={openEditEvent ? "open-edit" : "close-edit"}
        open={openEditEvent}
        setOpen={setOpenEditEvent}
        defaultValues={defaultAddEventValues}
        onEditEvent={handleEditEvent}
      /> */}
        <EditEvent
          profileId={profileId}
          open={openCreateEvent}
          handleClose={handleClose}
          //defaultValues={defaultAddEventValues}
          //onEditEvent={handleEditEvent}
          contractId={scheduleId}
          errors={errors}
          setFetch={setFetch}
          control={control}
          type={type}
          setValue={setValue}
          handleSubmit={handleSubmit}
          watch={watch}
          setEvents={setEvents}
          setSelectable={setSelectable}
        />
      </LocalizationProvider>
    </CalendarWarpper>
  );
}

export default BookingCalender;
