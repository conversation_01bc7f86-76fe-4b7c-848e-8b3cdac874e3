# StageMinder Run

The `Run` folder serves as a placeholder for both the StageMinder frontend and backend deployments. When the `deployStageMinder.sh` script is executed, it populates this directory with the necessary frontend and backend artifacts and configurations. Refer to the "Frontend" and "Backend" sections under Build for detailed build instructions.

## Overview

The `Run` folder contains subdirectories for the StageMinder frontend and backend, each populated with the required files to run the application. The deployment script (`deployStageMinder.sh`) copies artifacts from the build process into the respective `frontend` and `backend` subfolders.

### Folder Structure

The `Run` folder contains two main subdirectories:
- **frontend**: Contains the Next.js-based frontend artifacts.
- **backend**: Contains the backend artifacts and configurations.

#### Frontend Components

The following folders and files are copied into the `frontend` subdirectory during deployment:

1. **node_modules**
   - Contains all Next.js dependencies specified in `package.json`.
   - A soft symlink to this folder is used during the build process for generating the `.next` directory.

2. **.next**
   - Contains the compiled frontend artifacts generated during the build process.
   - Copied directly into the frontend directory during deployment.

3. **scripts**
   - Includes the `start.js` script, which loads configuration settings from the `.env` file.

4. **src**
   - Contains configuration files that map IP addresses from the `.env` file to the source code.

5. **next.config.mjs**
   - The Next.js configuration file, copied from the frontend build.

6. **package.json**
   - Specifies the project dependencies and scripts, copied from the frontend build.

7. **.env**
   - The environment configuration file, copied during deployment to configure runtime settings.

#### Backend Components

The following folders and files are copied into the `backend` subdirectory during deployment:

1. **config**
   - Contains the `application.properties` file, which holds configuration settings for the backend application.

2. **images**
   - Stores icon files used by the backend.

3. **json-files**
   - Contains JSON files that are loaded into the database during initialization or runtime.

4. **pdf-files**
   - Includes PDF templates (e.g., for riders) used by the backend for generating documents.

### Logs

The `logs` folder, located in the `Run` directory, contains the following log files for both frontend and backend:
- **hdb-server.log**: Rolls over daily to capture server logs.
- **StageServer.log**: Generated each time the server runs.

## Deployment Process

1. Run the `deployStageMinder.sh` script to initiate deployment.
2. The script copies the required frontend and backend artifacts into the `frontend` and `backend` subdirectories, respectively.
3. For the frontend, the `.env` file is copied to configure environment-specific settings, such as IP addresses.
4. For the backend, the `application.properties` file is used to configure settings, JSON files are loaded into the database, and PDF templates are made available for document generation.

## Running the Application

### Frontend
To start the StageMinder frontend:
1. Ensure the deployment script (`deployStageMinder.sh`) has populated the `frontend` subdirectory.
2. Navigate to the `frontend` directory and run the `start.js` script:
   ```bash
   cd frontend
   node scripts/start.js
