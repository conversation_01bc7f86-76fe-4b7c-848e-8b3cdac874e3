# StageMinder Run Frontend folder
It's a placeholder and when deployStageMinder.sh script ran, it will copy the frontend artifacts and configuration in this folder. Please refere Frontend under Build for the details.

The logs folder will have hdb-server.log rollover every day and StageServer.log when it runs everytime.

Following folders copied from Build are listed below.

1. node-modules - all the NextJS dependencies specified in the package.json is in this folder. The build folder has a soft symlink to this for the building the .next
2. .next - The built frontend is placed in the .next and that's copied directly here by the deployment.
3. scripts - start.js script file copied from the build folder to get the configuration via .env
4. src  - it only has config that map the IP address from .env to source
5. next.config.mjs and package.json copied from frontend build. 
During the deployment the dot.env get copied to .env
